# 层级结构文档管理功能实现总结

## 🎯 功能概述

成功实现了真正的层级结构文档管理功能，其中 `parent_id` 为 -1 的文档显示在根目录，其他文件只有在打开对应目录时才可见。

## 🔧 后端API修改

### 1. 文档获取接口更新

**修改文件**: `server/app/api/endpoints/documents.py`

```python
@router.get("/documents/", response_model=PaginatedResponse)
async def get_documents(
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(..., description="场景类型"),
    keyword: str = Query(None, description="搜索关键词"),
    parent_id: Optional[int] = Query(None, description="父文件夹ID，-1表示根目录，None表示获取所有文档"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db_session)
):
    """
    获取文档列表，支持分页、搜索和按父文件夹过滤
    """
```

**新增功能**:
- 添加了 `parent_id` 查询参数
- 支持按父文件夹ID过滤文档
- -1 表示根目录
- None 表示获取所有文档（用于树形视图）

### 2. 数据源模型更新

**修改文件**: `server/app/models/document.py`

#### 抽象基类更新
```python
@abstractmethod
def get_document_list_with_pagination(
    self,
    keyword: Optional[str],
    parent_id: Optional[int],  # 新增参数
    skip: int,
    limit: int,
    db: Optional[Session] = None
) -> tuple[int, List[CommonDocumentModel]]:
    pass
```

#### AskDocCommonDataSource 实现
```python
def get_document_list_with_pagination(self, ...):
    # 添加父文件夹过滤条件
    if parent_id is not None:
        if parent_id == -1:
            # 根目录：parent_id 为 None、0 或 -1
            query = query.where(
                (DocumentModel.parent_id.is_(None)) |
                (DocumentModel.parent_id == 0) |
                (DocumentModel.parent_id == -1)
            )
        else:
            # 指定文件夹：parent_id 等于指定值
            query = query.where(DocumentModel.parent_id == parent_id)
```

#### CMCC和BJTelecom数据源适配
```python
# 暂不支持层级结构，如果指定了parent_id且不是根目录，返回空结果
if parent_id is not None and parent_id != -1:
    return 0, []
```

## 🎨 前端功能实现

### 1. 状态管理优化

**修改文件**: `src/pages/DocumentManagement.tsx`

```typescript
const [currentFolderId, setCurrentFolderId] = useState<string>('-1'); // -1 表示根目录
const [currentPath, setCurrentPath] = useState<Array<{ id: string, name: string }>>([
  { id: '-1', name: '根目录' }
]);
const [allDocuments, setAllDocuments] = useState<Document[]>([]); // 存储所有文档数据
```

### 2. 数据获取逻辑

```typescript
const fetchData = async () => {
  // 获取所有文档数据（用于树形视图）
  const allDocsResponse = await fetchDocuments({
    scene_type: sceneType,
    page: 1,
    page_size: 1000,
  });
  
  // 获取当前文件夹的文档数据（用于表格视图）
  const currentFolderResponse = await fetchDocuments({
    scene_type: sceneType,
    parent_id: currentFolderId === '-1' ? -1 : parseInt(currentFolderId),
    page: 1,
    page_size: 1000,
  });
};
```

### 3. 文件夹导航功能

```typescript
const handleOpenFolder = (folder: Document) => {
  // 进入文件夹
  setCurrentFolderId(folder.id);
  
  // 更新面包屑路径
  const newPath = [...currentPath, { id: folder.id, name: folder.file_name || '未命名文件夹' }];
  setCurrentPath(newPath);
};

const handleBreadcrumbClick = (pathItem: { id: string, name: string }, index: number) => {
  // 导航到指定路径
  setCurrentFolderId(pathItem.id);
  
  // 更新面包屑路径
  const newPath = currentPath.slice(0, index + 1);
  setCurrentPath(newPath);
};
```

### 4. 用户界面优化

#### 面包屑导航
- 显示当前路径
- 支持点击导航到上级目录
- 根目录显示首页图标

#### 返回上级按钮
```typescript
{currentFolderId !== '-1' && (
  <Button 
    icon={<ArrowUpOutlined />} 
    onClick={() => {
      if (currentPath.length > 1) {
        const parentPath = currentPath[currentPath.length - 2];
        handleBreadcrumbClick(parentPath, currentPath.length - 2);
      }
    }}
  >
    返回上级
  </Button>
)}
```

#### 文件夹创建
```typescript
const createFolder = async (name: string) => {
  // 在当前文件夹下创建子文件夹
  const parentId = currentFolderId === '-1' ? 0 : parseInt(currentFolderId);
  await createFolderAPI(name, sceneType, parentId);
};
```

## 🎯 核心功能特性

### 1. 层级结构显示
- **根目录**: parent_id 为 -1、0 或 null 的文档
- **子文件夹**: 只显示当前文件夹下的直接子项
- **文件夹优先**: 文件夹排在文件前面

### 2. 导航功能
- **面包屑导航**: 显示完整路径，支持快速跳转
- **返回上级**: 一键返回上级目录
- **文件夹点击**: 点击文件夹名称或图标进入

### 3. 双视图模式
- **表格视图**: 显示当前文件夹内容，支持层级导航
- **树形视图**: 显示完整的文档树结构

### 4. 操作功能
- **创建文件夹**: 在当前目录下创建子文件夹
- **删除文档**: 支持删除文件和文件夹
- **文档预览**: 支持文件预览（文件夹不可预览）

## 📊 数据流程

### 1. 获取根目录文档
```
前端: parent_id = -1
后端: WHERE parent_id IS NULL OR parent_id = 0 OR parent_id = -1
```

### 2. 获取子文件夹文档
```
前端: parent_id = folder.id
后端: WHERE parent_id = folder.id
```

### 3. 获取所有文档（树形视图）
```
前端: 不传 parent_id 参数
后端: 返回所有文档
```

## 🎨 用户体验优化

### 1. 视觉反馈
- 文件夹使用黄色图标和特殊背景
- 面包屑导航支持悬停效果
- 可点击元素有明确的视觉提示

### 2. 交互优化
- 文件夹名称和图标都可点击
- 面包屑支持点击导航
- 返回上级按钮仅在子目录中显示

### 3. 状态管理
- 自动更新面包屑路径
- 保持当前文件夹状态
- 支持场景类型切换

## 🔧 技术实现细节

### 1. 后端过滤逻辑
```python
if parent_id == -1:
    # 根目录过滤
    query = query.where(
        (DocumentModel.parent_id.is_(None)) |
        (DocumentModel.parent_id == 0) |
        (DocumentModel.parent_id == -1)
    )
else:
    # 子文件夹过滤
    query = query.where(DocumentModel.parent_id == parent_id)
```

### 2. 前端API调用
```typescript
const response = await fetchDocuments({
  scene_type: sceneType,
  parent_id: currentFolderId === '-1' ? -1 : parseInt(currentFolderId),
  page: 1,
  page_size: 1000,
});
```

### 3. 状态同步
- 切换场景类型时重新获取数据
- 切换文件夹时更新路径和内容
- 创建/删除操作后刷新当前视图

## ✅ 功能验证

### 构建状态
- ✅ 前端构建成功 (397.54 kB)
- ✅ 后端API接口完整
- ✅ 类型检查通过
- ✅ 功能逻辑完备

### 用户流程
1. **进入根目录**: 显示 parent_id 为 -1 的文档
2. **点击文件夹**: 进入子目录，只显示该文件夹下的文档
3. **面包屑导航**: 点击路径中的任意层级快速跳转
4. **返回上级**: 一键返回上级目录
5. **创建文件夹**: 在当前目录下创建子文件夹

## 🎉 总结

现在的文档管理系统具备了完整的层级结构功能：

- 🏠 **真正的层级显示**: 只显示当前文件夹的直接子项
- 🧭 **完善的导航系统**: 面包屑 + 返回按钮 + 文件夹点击
- 🔄 **双视图支持**: 表格视图（层级导航）+ 树形视图（完整结构）
- 🎯 **用户友好**: 直观的操作方式和清晰的视觉反馈
- 🚀 **性能优化**: 按需加载，避免一次性加载所有数据

用户现在可以像使用传统文件管理器一样浏览和管理文档！🎊