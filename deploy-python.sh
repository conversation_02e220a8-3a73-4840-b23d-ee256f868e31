#!/bin/bash

path="/data/home/<USER>/doc-evaluate-studio"
echo "开始部署后端"
echo $path

rsync -av --exclude='*.pyc' --exclude='venv' --exclude='__pycache__' ./server/ yuanyuan@192.168.110.100:$path/server/

scp python-compose.yaml yuanyuan@192.168.110.100:$path/
scp python.dockerfile yuanyuan@192.168.110.100:$path/

ssh yuanyuan@192.168.110.100 "cd $path && DOCKER_BUILDKIT=0 docker compose -f python-compose.yaml build && docker compose -f python-compose.yaml up -d"
