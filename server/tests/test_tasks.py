import pytest
from datetime import datetime
from app.models.task import Task
from app.schemas.task import TaskCreate, TaskUpdate
from tests.conftest import async_client

@pytest.fixture
def sample_task():
    """样例任务数据"""
    return {
        "id": 1,
        "name": "测试任务",
        "sample_set_id": "sample-set-1",
        "collection_name": "test_collection",
        "top_k": 50,
        "rerank_k": 5,
        "status": "pending",
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }

def test_create_task(test_db, sample_task):
    """测试创建任务"""
    task_create = TaskCreate(
        name="测试任务",
        sample_set_id="sample-set-1",
        collection_name="test_collection"
    )
    
    task = Task.create(test_db, task_create)
    
    assert task.name == task_create.name
    assert task.sample_set_id == task_create.sample_set_id
    assert task.collection_name == task_create.collection_name
    assert task.top_k == 50
    assert task.rerank_k == 5
    assert len(test_db.items) == 1

def test_get_task(test_db, sample_task):
    """测试获取任务"""
    test_db.items.append(Task(**sample_task))
    task = Task.get_by_id(test_db, 1)
    
    assert task.id == sample_task["id"]
    assert task.name == sample_task["name"]
    assert task.sample_set_id == sample_task["sample_set_id"]

def test_get_all_tasks(test_db, sample_task):
    """测试获取任务列表"""
    test_db.items.append(Task(**sample_task))
    tasks = Task.get_all(test_db, skip=0, limit=10)
    
    assert len(tasks) == 1
    assert tasks[0].id == sample_task["id"]

def test_update_task(test_db, sample_task):
    """测试更新任务"""
    task = Task(**sample_task)
    test_db.items.append(task)
    
    task_update = TaskUpdate(
        name="更新后的任务名",
        sample_set_id="sample-set-1",
        collection_name="test_collection"
    )
    
    updated_task = task.update(test_db, task_update)
    
    assert updated_task.name == "更新后的任务名"
    assert test_db.committed

def test_delete_task(test_db, sample_task):
    """测试删除任务"""
    task = Task(**sample_task)
    test_db.items.append(task)
    
    result = task.delete(test_db)
    
    assert result is True
    assert len(test_db.items) == 0
    assert test_db.committed

def test_create_task_without_name(test_db):
    """测试创建任务时不提供名称"""
    task_create = TaskCreate(
        sample_set_id="sample-set-1",
        collection_name="test_collection"
    )
    
    task = Task.create(test_db, task_create)
    
    assert task.name.startswith("样本测试_")
    assert task.sample_set_id == task_create.sample_set_id
    assert len(test_db.items) == 1

@pytest.mark.asyncio
async def test_task_api_endpoints(async_client):
    """测试任务API端点"""
    # 测试创建任务
    create_response = await async_client.post("/api/tasks", json={
        "name": "API测试任务",
        "sample_set_id": "sample-set-1",
        "collection_name": "test_collection"
    })
    assert create_response.status_code == 200
    data = create_response.json()
    assert data["name"] == "API测试任务"
    
    task_id = data["id"]
    
    # 测试获取任务列表
    list_response = await async_client.get("/api/tasks")
    assert list_response.status_code == 200
    tasks = list_response.json()
    assert isinstance(tasks, list)
    
    # 测试获取单个任务
    get_response = await async_client.get(f"/api/tasks/{task_id}")
    assert get_response.status_code == 200
    
    # 测试更新任务
    update_response = await async_client.put(
        f"/api/tasks/{task_id}",
        json={
            "name": "更新的任务名",
            "sample_set_id": "sample-set-1",
            "collection_name": "test_collection"
        }
    )
    assert update_response.status_code == 200
    updated_data = update_response.json()
    assert updated_data["name"] == "更新的任务名"
    
    # 测试删除任务
    delete_response = await async_client.delete(f"/api/tasks/{task_id}")
    assert delete_response.status_code == 200 