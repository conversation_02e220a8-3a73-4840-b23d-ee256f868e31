import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from app.services.task_scheduler import TaskScheduler
from app.services.task_executor import TaskExecutor
from app.models.task import Task
from app.models.sample import Sample
from app.models.sample_result import SampleResult

@pytest.fixture
def mock_sample():
    """模拟样本数据"""
    return Sample(
        id="test-sample-1",
        question="测试问题",
        sample_set_id="test-set-1",
        documents=[{
            "document_id": "doc1",
            "document_name": "测试文档",
            "expected_node_ids": ["node1", "node2"]
        }]
    )

@pytest.fixture
def mock_samples_response():
    """模拟样本列表响应"""
    return Mock(
        items=[Mock(
            id="test-sample-1",
            question="测试问题",
            documents=[{
                "document_id": "doc1",
                "expected_node_ids": ["node1", "node2"]
            }]
        )],
        total=1
    )

@pytest.fixture
def task_scheduler():
    """提供任务调度器实例"""
    return TaskScheduler()

@pytest.mark.asyncio
async def test_schedule_task(task_scheduler, test_db, mock_samples_response):
    """测试任务调度"""
    # 创建测试任务
    task = Task(
        id=1,
        name="测试任务",
        sample_set_id="test-set-1",
        collection_name="test_collection",
        status="pending"
    )
    test_db.add(task)

    # 模拟样本服务
    with patch('app.services.sample_service.SampleService.get_samples') as mock_get_samples:
        mock_get_samples.return_value = mock_samples_response
        
        # 执行任务
        await task_scheduler.schedule_task(test_db, task)
        
        # 验证任务状态
        assert task.status == "completed"
        assert task.result is not None
        assert "total_samples" in task.result
        assert task.result["total_samples"] == 1

@pytest.mark.asyncio
async def test_concurrent_task_execution(task_scheduler, test_db):
    """测试并发任务执行"""
    # 创建多个测试任务
    tasks = [
        Task(
            id=i,
            name=f"测试任务{i}",
            sample_set_id="test-set-1",
            collection_name="test_collection",
            status="pending"
        )
        for i in range(1, 4)
    ]
    for task in tasks:
        test_db.add(task)

    # 并发执行任务
    import asyncio
    await asyncio.gather(
        *[task_scheduler.schedule_task(test_db, task) for task in tasks]
    )

    # 验证所有任务都已完成
    for task in tasks:
        assert task.status in ["completed", "failed"]

@pytest.mark.asyncio
async def test_task_failure_handling(task_scheduler, test_db):
    """测试任务失败处理"""
    # 创建测试任务
    task = Task(
        id=1,
        name="测试任务",
        sample_set_id="test-set-1",
        collection_name="test_collection",
        status="pending"
    )
    test_db.add(task)

    # 模拟执行器抛出异常
    with patch.object(TaskExecutor, 'execute_task', side_effect=Exception("测试异常")):
        await task_scheduler.schedule_task(test_db, task)
        
        # 验证任务状态
        assert task.status == "failed"
        assert "error" in task.result

def test_task_running_status(task_scheduler):
    """测试任务运行状态跟踪"""
    task_id = 1
    
    # 初始状态
    assert not task_scheduler.is_task_running(task_id)
    
    # 添加运行中的任务
    task_scheduler._running_tasks.add(task_id)
    assert task_scheduler.is_task_running(task_id)
    
    # 移除任务
    task_scheduler._running_tasks.remove(task_id)
    assert not task_scheduler.is_task_running(task_id)

@pytest.mark.asyncio
async def test_sample_result_storage(task_scheduler, test_db, mock_samples_response):
    """测试样本结果存储"""
    # 创建测试任务
    task = Task(
        id=1,
        name="测试任务",
        sample_set_id="test-set-1",
        collection_name="test_collection",
        status="pending"
    )
    test_db.add(task)

    # 模拟样本服务
    with patch('app.services.sample_service.SampleService.get_samples') as mock_get_samples:
        mock_get_samples.return_value = mock_samples_response
        
        # 执行任务
        await task_scheduler.schedule_task(test_db, task)
        
        # 验证样本结果
        results = test_db.query(SampleResult).filter(SampleResult.task_id == task.id).all()
        assert len(results) > 0
        for result in results:
            assert result.task_id == task.id
            assert result.sample_id is not None
            assert result.result is not None 