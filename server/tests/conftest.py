import os
import sys
import pytest
from pathlib import Path
from httpx import AsyncClient, ASGITransport
import pytest_asyncio

# 添加项目根目录到 Python 路径
root_dir = str(Path(__file__).parent.parent)
sys.path.insert(0, root_dir)

# 导入测试所需的依赖
from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_biz_db

pytest_plugins = ('pytest_asyncio',)

class MockSession:
    def __init__(self):
        self.committed = False
        self.rolled_back = False
        self.closed = False
        self.items = []
        self._offset = 0
        self._limit = None

    def commit(self):
        self.committed = True

    def rollback(self):
        self.rolled_back = True

    def close(self):
        self.closed = True

    def add(self, item):
        self.items.append(item)

    def refresh(self, item):
        # 模拟刷新操作
        pass

    def query(self, *args, **kwargs):
        return self

    def filter(self, *args, **kwargs):
        return self

    def offset(self, offset):
        self._offset = offset
        return self

    def limit(self, limit):
        self._limit = limit
        return self

    def first(self):
        return self.items[0] if self.items else None

    def all(self):
        if self._limit is not None:
            return self.items[self._offset:self._offset + self._limit]
        return self.items[self._offset:]

    def delete(self, item):
        if item in self.items:
            self.items.remove(item)

@pytest.fixture
def test_db():
    """提供测试数据库会话"""
    return MockSession()

@pytest.fixture
def client(test_db):
    """提供测试客户端"""
    def override_get_db():
        return test_db
    
    app.dependency_overrides[get_biz_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()

def get_test_db():
    """同步数据库会话依赖"""
    return MockSession()

@pytest_asyncio.fixture
async def async_client(test_db):
    """提供异步测试客户端"""
    app.dependency_overrides[get_biz_db] = lambda: test_db
    async with AsyncClient(
        transport=ASGITransport(app=app),
        base_url="http://test"
    ) as client:
        yield client
    app.dependency_overrides.clear() 