import os
import subprocess
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def convert_docx_to_pdf(docx_path: str, output_dir: str = None) -> str:
    """
    将docx文件转换为pdf文件
    
    Args:
        docx_path: docx文件路径
        output_dir: 输出目录,如果为None则使用docx所在目录
        
    Returns:
        生成的pdf文件路径
        
    Raises:
        Exception: 转换失败时抛出异常
    """
    try:
        docx_path = Path(docx_path)
        if not docx_path.exists():
            raise FileNotFoundError(f"找不到文件: {docx_path}")
            
        if output_dir is None:
            output_dir = docx_path.parent
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
        pdf_path = output_dir / f"{docx_path.stem}.pdf"
        
        # 使用libreoffice进行转换
        cmd = [
            'libreoffice',
            '--headless',
            '--convert-to',
            'pdf',
            '--outdir',
            str(output_dir),
            str(docx_path)
        ]
        
        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        if process.returncode != 0:
            raise Exception(f"转换失败: {process.stderr}")
            
        if not pdf_path.exists():
            raise Exception("PDF文件未生成")
            
        return str(pdf_path)
        
    except Exception as e:
        logger.error(f"转换docx到pdf失败: {str(e)}")
        raise


if __name__ == "__main__":
    print(convert_docx_to_pdf("test.docx"))
