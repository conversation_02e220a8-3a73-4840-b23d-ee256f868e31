import os
import shutil
from pathlib import Path
import logging
from .docx2pdf import convert_docx_to_pdf
from .pptx2pdf import convert_pptx_to_pdf
from .xlsx2pdf import convert_xlsx_to_pdf
from .txt2pdf import convert_txt_to_pdf

logger = logging.getLogger(__name__)

def transfer_history_files():
    """
    遍历/data3/mock_upload目录,将非PDF文件转换为PDF并保存到新目录
    
    处理满足fileid/filename格式的文件路径
    如果不是PDF文件,根据文件类型调用相应转换接口
    转换后的PDF文件保存到fileid-pdf/filename路径
    """
    base_dir = Path("/data3/mock_upload")
    
    # 文件类型到转换函数的映射
    converters = {
        ".docx": convert_docx_to_pdf,
        ".pptx": convert_pptx_to_pdf, 
        ".xlsx": convert_xlsx_to_pdf,
        ".txt": convert_txt_to_pdf
    }
    
    try:
        # 遍历所有文件
        for file_path in base_dir.rglob("*"):
            
            if not file_path.is_file():
                logger.warning(f"不是文件: {file_path}")
                continue
                
            # 检查路径格式是否为fileid/filename
            try:
                relative_path = file_path.relative_to(base_dir)
                parts = relative_path.parts
                if len(parts) != 2:
                    logger.warning(f"路径格式错误: {file_path}")
                    continue
                    
                fileid, filename = parts
            except ValueError:
                logger.warning(f"路径格式错误: {file_path}")
                continue
                
            # 如果已经是PDF文件则跳过
            if file_path.suffix.lower() == ".pdf":
                logger.info(f"已经是PDF文件: {file_path}")
                continue
                
            # 获取对应的转换函数
            converter = converters.get(file_path.suffix.lower())
            if not converter:
                logger.warning(f"不支持的文件类型: {file_path}")
                continue
                
            # 创建目标目录
            dest_dir = base_dir / f"{fileid}-pdf"
            dest_dir.mkdir(parents=True, exist_ok=True)
            
            try:
                # 转换文件
                logger.info(f"开始转换文件: {file_path}")
                pdf_path = converter(str(file_path), str(dest_dir))
                logger.info(f"成功转换文件: {file_path} -> {pdf_path}")
            except Exception as e:
                logger.error(f"转换文件失败 {file_path}: {str(e)}")
                
    except Exception as e:
        logger.error(f"处理文件时发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    # 配置日志同时输出到控制台和文件
    log_file = 'transfer_history.log'
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    transfer_history_files()
