from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.config.config import DB_CONFIGS
from typing import Dict

# 为每个场景创建独立的引擎
engines: Dict[str, any] = {
    scene_type: create_engine(
        url,
        echo=False,
        pool_recycle=300
    ) for scene_type, url in DB_CONFIGS.items()
}

# 为每个场景创建独立的 SessionLocal
session_factories = {
    scene_type: sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    ) for scene_type, engine in engines.items()
}

def get_db(scene_type: str):
    """
    获取指定场景的数据库会话
    """
    if scene_type not in session_factories:
        raise ValueError(f"Unknown scene type: {scene_type}")
        
    return session_factories[scene_type]()

def get_biz_db():
    return session_factories["biz"]()
