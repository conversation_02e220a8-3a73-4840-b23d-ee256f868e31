from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import documents, samples, tasks

app = FastAPI(
    title="Document Evaluation API",
    description="API for document evaluation system",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(documents.router, prefix="/api", tags=["documents"])
app.include_router(samples.router, prefix="/api", tags=["samples"])
app.include_router(tasks.router, prefix="/api", tags=["tasks"])
