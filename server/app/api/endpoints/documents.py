from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import List, Literal, Optional
from app.core.database import get_db
from app.schemas.document import (
    CommonDocumentModel,
    DocumentQueryParams,
    PaginatedResponse,
    SceneType,
    NodeQueryParams,
    NodeQueryResponse,
)
from app.models.document import (
    AskDocCommonDataSource,
    CMCCDocDataSource,
    BJTelecomDocumentData,
)
from app.services.milvus_service import MilvusService
from app.utils.document_preview import get_document_preview_url

router = APIRouter()


def get_data_source(scene_type: str):
    sources = {
        "common": AskDocCommonDataSource(),
        "bj_telecom": BJTelecomDocumentData(),
        "cmcc": CMCCDocDataSource(),
    }
    if scene_type not in sources:
        raise ValueError(f"Unknown scene type: {scene_type}")
    return sources[scene_type]


async def get_db_session(
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(...),
):
    db = get_db(scene_type)
    try:
        yield db
    finally:
        db.close()


@router.get("/documents/", response_model=PaginatedResponse)
async def get_documents(
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(
        ..., description="场景类型"
    ),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    parent_id: Optional[int] = Query(
        None, description="父文件夹ID，-1表示根目录，None表示获取所有文档"
    ),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    db: Session = Depends(get_db_session),
):
    """
    获取文档列表，支持分页、搜索和按父文件夹过滤
    """
    data_source = get_data_source(scene_type)
    skip = (page - 1) * page_size

    total, items = data_source.get_document_list_with_pagination(
        keyword=keyword, parent_id=parent_id, skip=skip, limit=page_size, db=db
    )

    return PaginatedResponse(total=total, items=items)


milvus_service = MilvusService()


@router.get("/documents/nodes", response_model=NodeQueryResponse)
async def get_document_nodes(
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(
        ..., description="场景类型"
    ),
    file_id: str = Query(..., description="文档ID"),
):
    """
    获取文档的节点列表
    """
    try:
        nodes = milvus_service.get_nodes_by_file_id(scene_type, file_id)
        return NodeQueryResponse(nodes=nodes)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Error fetching nodes: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents/{file_id}/download-url")
async def get_document_download_url(
    file_id: str,
    file_name: str = Query(..., description="文件名"),
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(...),
):
    """获取文档的下载URL"""
    try:
        url = get_document_preview_url(scene_type, file_id, file_name)
        if not url:
            raise HTTPException(
                status_code=404, detail="Document preview URL not found"
            )
        return {"url": url}
    except Exception as e:
        print(f"Error getting document URL: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents/collections")
async def get_collections():
    """获取所有可用的 Milvus collections"""
    try:
        collections = milvus_service.get_all_collections()
        return {"code": 0, "msg": "success", "data": collections}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get collections: {str(e)}"
        )


@router.delete("/documents/{file_id}")
async def delete_document(
    file_id: str,
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(...),
    db: Session = Depends(get_db_session),
):
    """删除文档"""
    try:
        data_source = get_data_source(scene_type)
        success = data_source.delete_document(file_id, db)
        if not success:
            raise HTTPException(status_code=404, detail="Document not found")
        return {"message": "Document deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/documents/folder")
async def create_folder(
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(...),
    folder_data: dict = None,
    db: Session = Depends(get_db_session),
):
    """创建文件夹"""
    try:
        if scene_type != "common":
            raise HTTPException(
                status_code=400, detail="Only common scene supports folder creation"
            )

        folder_name = folder_data.get("name") if folder_data else None
        parent_id = folder_data.get("parent_id", 0) if folder_data else 0

        if not folder_name:
            raise HTTPException(status_code=400, detail="Folder name is required")

        # 处理parent_id：如果是0或-1，设置为None（表示根目录）
        if parent_id in [0, -1]:
            parent_id = None

        # 创建文件夹记录
        from app.schemas.document import DocumentModel

        new_folder = DocumentModel(
            name=folder_name,
            file_type="folder",
            is_dir=True,
            parent_id=parent_id,
            file_status=1,
            size=0,
            mime_type="",
            source_path="",
            language="zh",  # 设置默认语言
            overview="",
        )

        db.add(new_folder)
        db.commit()
        db.refresh(new_folder)

        return {"message": "Folder created successfully", "id": new_folder.id}
    except Exception as e:
        db.rollback()
        print(f"Error creating folder: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents/folder-path/{folder_id}")
async def get_folder_path(
    folder_id: str,
    scene_type: Literal["common", "bj_telecom", "cmcc"] = Query(...),
    db: Session = Depends(get_db_session),
):
    """获取文件夹的完整路径（面包屑导航用）"""
    try:
        if scene_type != "common":
            raise HTTPException(
                status_code=400, detail="Only common scene supports folder operations"
            )

        from app.schemas.document import DocumentModel

        path = []
        current_id = int(folder_id) if folder_id != "-1" else None

        # 从当前文件夹向上遍历到根目录
        while current_id is not None:
            folder = (
                db.query(DocumentModel)
                .filter(DocumentModel.id == current_id, DocumentModel.is_dir == True)
                .first()
            )

            if not folder:
                break

            path.insert(0, {"id": str(folder.id), "name": folder.name})

            current_id = folder.parent_id

            # 防止无限循环
            if len(path) > 10:
                break

        # 添加根目录
        path.insert(0, {"id": "-1", "name": "根目录"})

        return {"path": path}
    except Exception as e:
        print(f"Error getting folder path: {e}")
        raise HTTPException(status_code=500, detail=str(e))
