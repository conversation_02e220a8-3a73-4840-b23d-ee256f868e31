from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import List, Literal
from app.core.database import get_biz_db
from app.schemas.document import (
    SampleSet, Sample, SampleCreate, SampleSetCreate,
    SampleSetResponse, SampleResponse
)
from app.services.sample_service import SampleService
from app.models.sample import SampleSetModel

router = APIRouter(prefix="/samples")
sample_service = SampleService()

async def get_db_session():
    db = get_biz_db()
    try:
        yield db
    finally:
        db.close()
        
@router.post("/sample-sets", response_model=SampleSet)
async def create_sample_set(
    data: SampleSetCreate,
    db: Session = Depends(get_db_session)
):
    return sample_service.create_sample_set(db, data)

@router.get("/sample-sets", response_model=SampleSetResponse)
async def get_sample_sets(
    scene_type: str = Query(..., description="场景类型"),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db_session)
):
    """获取样本集列表"""
    try:
        response = sample_service.get_sample_sets(db, scene_type, skip, limit)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("", response_model=Sample)
async def create_sample(
    data: SampleCreate,
    db: Session = Depends(get_db_session)
):
    try:
        return await sample_service.create_sample(db, data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Error creating sample: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("", response_model=SampleResponse)
async def get_samples(
    sample_set_id: str = Query(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db_session)
):
    skip = (page - 1) * page_size
    response = sample_service.get_samples(db, sample_set_id, skip, page_size)
    return response

@router.put("/{sample_id}", response_model=Sample)
async def update_sample(
    sample_id: str,
    data: SampleCreate,
    db: Session = Depends(get_db_session)
):
    """更新样本"""
    try:
        return sample_service.update_sample(db, sample_id, data)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error updating sample: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{sample_id}", status_code=204)
async def delete_sample(
    sample_id: str,
    db: Session = Depends(get_db_session)
):
    """删除样本"""
    try:
        sample_service.delete_sample(db, sample_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error deleting sample: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/sample-sets/{sample_set_id}", response_model=SampleSet)
def update_sample_set(
    sample_set_id: str,
    data: dict,
    db: Session = Depends(get_db_session)
):
    """更新样本集"""
    sample_set = db.query(SampleSetModel).filter(SampleSetModel.id == sample_set_id).first()
    if not sample_set:
        raise HTTPException(status_code=404, detail="Sample set not found")
    
    # 更新名称
    if "name" in data:
        sample_set.name = data["name"]
    
    try:
        db.commit()
        db.refresh(sample_set)
        return sample_set
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e)) 