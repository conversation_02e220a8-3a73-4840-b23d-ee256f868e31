from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_biz_db
from app.schemas.task import Task as TaskSchema, TaskCreate, TaskUpdate
from app.models.task import Task
from app.services.task_scheduler import TaskScheduler
from app.models.sample_result import SampleResult

router = APIRouter()
task_scheduler = TaskScheduler()

async def get_db_session():
    db = get_biz_db()
    try:
        yield db
    finally:
        db.close()

@router.post("/tasks", response_model=TaskSchema)
async def create_task(
    task_in: TaskCreate,
    db: Session = Depends(get_db_session)
):
    """创建新任务"""
    return Task.create(db=db, task=task_in)

@router.get("/tasks", response_model=List[TaskSchema])
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db_session)
):
    """获取任务列表"""
    return Task.get_all(db=db, skip=skip, limit=limit)

@router.get("/tasks/{task_id}", response_model=TaskSchema)
async def get_task(
    task_id: int,
    db: Session = Depends(get_db_session)
):
    """获取特定任务"""
    db_task = Task.get_by_id(db=db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    return db_task

@router.put("/tasks/{task_id}", response_model=TaskSchema)
async def update_task(
    task_id: int,
    task_in: TaskUpdate,
    db: Session = Depends(get_db_session)
):
    """更新任务"""
    db_task = Task.get_by_id(db=db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    return db_task.update(db=db, task_update=task_in)

@router.delete("/tasks/{task_id}")
async def delete_task(
    task_id: int,
    db: Session = Depends(get_db_session)
):
    """删除任务"""
    db_task = Task.get_by_id(db=db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    db_task.delete(db=db)
    return {"message": "Task deleted successfully"}

@router.get("/tasks/{task_id}/result")
async def get_task_result(
    task_id: int,
    db: Session = Depends(get_db_session)
):
    """获取任务结果详情"""
    db_task = Task.get_by_id(db=db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 获取任务的样本结果统计
    sample_results = db.query(SampleResult).filter(SampleResult.task_id == task_id).all()
    
    # 计算统计信息
    total_samples = len(sample_results)
    success_samples = len([r for r in sample_results if r.exec_status == 'success'])
    failed_samples = len([r for r in sample_results if r.exec_status == 'failed'])
    recall_accurate_samples = len([r for r in sample_results if r.recall_accuracy])
    rerank_accurate_samples = len([r for r in sample_results if r.rerank_accuracy])
    
    return {
        "task": db_task,
        "statistics": {
            "total_samples": total_samples,
            "success_samples": success_samples,
            "failed_samples": failed_samples,
            "recall_accurate_samples": recall_accurate_samples,
            "rerank_accurate_samples": rerank_accurate_samples,
            "recall_accuracy_rate": recall_accurate_samples / total_samples if total_samples > 0 else 0,
            "rerank_accuracy_rate": rerank_accurate_samples / total_samples if total_samples > 0 else 0,
            "success_rate": success_samples / total_samples if total_samples > 0 else 0
        }
    }

@router.post("/tasks/{task_id}/execute")
async def execute_task(
    task_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db_session)
):
    """执行任务"""
    db_task = Task.get_by_id(db, task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if task_scheduler.is_task_running(task_id):
        raise HTTPException(status_code=400, detail="Task is already running")
    
    background_tasks.add_task(task_scheduler.schedule_task, db, db_task)
    return {"message": "Task execution started"}

@router.get("/tasks/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: Session = Depends(get_db_session)
):
    """获取任务状态"""
    db_task = Task.get_by_id(db, task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return {
        "status": db_task.status,
        "is_running": task_scheduler.is_task_running(task_id)
    }

@router.get("/tasks/{task_id}/sample-results")
async def get_task_sample_results(
    task_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db_session)
):
    """获取任务的样本结果"""
    db_task = Task.get_by_id(db, task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    
    results = db.query(SampleResult)\
        .filter(SampleResult.task_id == task_id)\
        .offset(skip)\
        .limit(limit)\
        .all()
    
    return {
        "total": db.query(SampleResult).filter(SampleResult.task_id == task_id).count(),
        "items": results
    } 