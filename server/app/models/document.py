from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union, Sequence
from sqlalchemy import (
    DateTime, Integer, Text, UniqueConstraint, create_engine,
    func, select, update, delete, Column, JSON, text, Boolean, Enum
)
from sqlalchemy.orm import Session, sessionmaker
from app.schemas.document import DocumentModel, CMCCFileModel, BjTelecomFile, CommonDocumentModel
from app.core.database import get_db
from abc import ABC, abstractmethod
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class DocumentDataSource(ABC):
    def __init__(self):
        self.scene_type = None  # 子类需要设置

    @abstractmethod
    def get_document_list_with_pagination(
        self,
        keyword: Optional[str],
        parent_id: Optional[int],
        skip: int,
        limit: int,
        db: Optional[Session] = None
    ) -> tuple[int, List[CommonDocumentModel]]:
        pass

    @abstractmethod
    def delete_document(self, file_id: str, db: Session) -> bool:
        """删除文档，返回是否成功"""
        pass


class AskDocCommonDataSource(DocumentDataSource):
    def __init__(self):
        super().__init__()
        self.scene_type = "common"
    
    def _get_upload_status_from_file_status(self, file_status: int, is_dir: bool) -> str:
        """根据file_status映射到upload_status"""
        if is_dir:
            return ""  # 文件夹不显示状态
        
        # 根据file_status映射状态
        status_map = {
            0: "UPLOAD_COMPLETED",      # 上传完成
            1: "INDEX_BUILDING",        # 索引构建中
            2: "INDEX_BUILD_SUCCESS",   # 索引构建成功
            3: "INDEX_BUILD_FAILED",    # 索引构建失败
        }
        return status_map.get(file_status, "UPLOAD_COMPLETED")

    def get_document_list_with_pagination(
        self,
        keyword: Optional[str],
        parent_id: Optional[int],
        skip: int,
        limit: int,
        db: Optional[Session] = None
    ) -> tuple[int, List[CommonDocumentModel]]:
        session = db
        try:
            query = select(DocumentModel)
            
            # 添加关键词搜索条件
            if keyword:
                query = query.where(DocumentModel.name.ilike(f"%{keyword}%"))
            
            # 添加父文件夹过滤条件
            if parent_id is not None:
                if parent_id == -1:
                    # 根目录：parent_id 为 None、0 或 -1
                    query = query.where(
                        (DocumentModel.parent_id.is_(None)) |
                        (DocumentModel.parent_id == 0) |
                        (DocumentModel.parent_id == -1)
                    )
                else:
                    # 指定文件夹：parent_id 等于指定值
                    query = query.where(DocumentModel.parent_id == parent_id)
            
            # 按照层级结构排序：文件夹在前，然后按名称排序
            query = query.order_by(
                DocumentModel.id.desc(),
                DocumentModel.is_dir.desc(),
            )
            
            total = session.scalar(select(func.count()).select_from(query.subquery()))
            
            documents = session.scalars(
                query.offset(skip).limit(limit)
            ).all()
            
            return total, [
                CommonDocumentModel(
                    id=str(doc.id),
                    file_name=doc.name,
                    file_type=doc.file_type,
                    source_url=doc.source_path,
                    file_status=doc.file_status,
                    upload_status=self._get_upload_status_from_file_status(doc.file_status, doc.is_dir),
                    is_dir=doc.is_dir,
                    parent_id=doc.parent_id,
                ) for doc in documents
            ]
        finally:
            if not db:
                session.close()

    def delete_document(self, file_id: str, db: Session) -> bool:
        """删除通用文档"""
        try:
            result = db.execute(
                delete(DocumentModel).where(DocumentModel.id == int(file_id))
            )
            db.commit()
            return result.rowcount > 0
        except Exception as e:
            db.rollback()
            print(f"Error deleting common document {file_id}: {e}")
            return False


class CMCCDocDataSource(DocumentDataSource):
    def get_document_list_with_pagination(
        self,
        keyword: Optional[str],
        parent_id: Optional[int],
        skip: int,
        limit: int,
        db: Optional[Session] = None
    ) -> tuple[int, List[CommonDocumentModel]]:
        session = db
        try:
            query = select(CMCCFileModel)
            if keyword:
                query = query.where(CMCCFileModel.file_name.ilike(f"%{keyword}%"))
            
            # CMCC数据源暂不支持层级结构，忽略parent_id参数
            # 如果指定了parent_id且不是根目录，返回空结果
            if parent_id is not None and parent_id != -1:
                return 0, []
            
            total = session.scalar(select(func.count()).select_from(query.subquery()))
            
            documents = session.scalars(
                query.offset(skip).limit(limit)
            ).all()
            
            return total, [
                CommonDocumentModel(
                    id=doc.file_id,
                    file_name=doc.file_name,
                    file_type=doc.file_type,
                    source_url=doc.file_id,
                    upload_status=doc.upload_status,
                    platform=doc.platform,
                    upload_type=doc.upload_type,
                    app_id=doc.app_id,
                    x_location=doc.x_location,
                    is_dir=False,  # CMCC文档都是文件，不是文件夹
                    parent_id=-1,  # 都在根目录
                ) for doc in documents
            ]
        finally:
            if not db:
                session.close()

    def delete_document(self, file_id: str, db: Session) -> bool:
        """删除CMCC文档"""
        try:
            result = db.execute(
                delete(CMCCFileModel).where(CMCCFileModel.file_id == file_id)
            )
            db.commit()
            return result.rowcount > 0
        except Exception as e:
            db.rollback()
            print(f"Error deleting CMCC document {file_id}: {e}")
            return False


class BJTelecomDocumentData(DocumentDataSource):
    def get_document_list_with_pagination(
        self,
        keyword: Optional[str],
        parent_id: Optional[int],
        skip: int,
        limit: int,
        db: Optional[Session] = None
    ) -> tuple[int, List[CommonDocumentModel]]:
        session = db
        try:
            query = select(BjTelecomFile)
            if keyword:
                query = query.where(BjTelecomFile.file_name.ilike(f"%{keyword}%"))
            
            # BJTelecom数据源暂不支持层级结构，忽略parent_id参数
            # 如果指定了parent_id且不是根目录，返回空结果
            if parent_id is not None and parent_id != -1:
                return 0, []
            
            total = session.scalar(select(func.count()).select_from(query.subquery()))
            
            documents = session.scalars(
                query.offset(skip).limit(limit)
            ).all()
            
            return total, [
                CommonDocumentModel(
                    id=doc.file_id,
                    file_name=doc.file_name,
                    file_type=doc.file_type,
                    source_url=doc.meta_info.get("url", doc.file_id) if doc.meta_info else doc.file_id,
                    upload_status=doc.upload_status,
                    meta_info=doc.meta_info,
                    is_dir=False,  # BJTelecom文档都是文件，不是文件夹
                    parent_id=-1,  # 都在根目录
                ) for doc in documents
            ]
        finally:
            if not db:
                session.close()

    def delete_document(self, file_id: str, db: Session) -> bool:
        """删除北京电信文档"""
        try:
            result = db.execute(
                delete(BjTelecomFile).where(BjTelecomFile.file_id == file_id)
            )
            db.commit()
            return result.rowcount > 0
        except Exception as e:
            db.rollback()
            print(f"Error deleting BJ Telecom document {file_id}: {e}")
            return False
