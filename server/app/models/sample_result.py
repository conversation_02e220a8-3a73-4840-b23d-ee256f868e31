from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, JSON, ForeignKey
from datetime import datetime
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class SampleResult(Base):
    __tablename__ = "sample_results"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False)
    sample_id = Column(String(255), nullable=False)
    question = Column(String(1000), nullable=False)
    # 原始数据
    original_data = Column(JSON, nullable=False)
    # 执行结果
    exec_status = Column(String(255), nullable=False)
    exec_msg = Column(String(255), nullable=True)
    # 召回准确与否
    recall_accuracy = Column(Boolean, nullable=True)
    # 重排准确与否
    rerank_accuracy = Column(Boolean, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)