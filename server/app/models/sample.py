from datetime import datetime
from typing import Dict, List
import uuid
from sqlalchemy import String, DateTime, JSON, ForeignKey, Text
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class SampleSetModel(Base):
    __tablename__ = "sample_sets"

    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    scene_type: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )

class Sample(Base):
    __tablename__ = "samples"

    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    question: Mapped[str] = mapped_column(Text, nullable=False)
    sample_set_id: Mapped[str] = mapped_column(String(36), ForeignKey("sample_sets.id"), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class SampleDocument(Base):
    __tablename__ = "sample_documents"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    sample_id: Mapped[str] = mapped_column(String(36), ForeignKey("samples.id"), nullable=False)
    document_id: Mapped[str] = mapped_column(String(36), nullable=False)
    document_name: Mapped[str] = mapped_column(String(255), nullable=False)
    expected_node_ids: Mapped[List[str]] = mapped_column(JSON, nullable=False)
    node_contents: Mapped[Dict[str, str]] = mapped_column(JSON, nullable=False)  # 存储节点ID到内容的映射
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow) 
