from sqlalchemy import Column, Float, Integer, String, DateTime, Enum, JSO<PERSON>
from datetime import datetime
from typing import List, Optional
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.task import TaskCreate, TaskUpdate 
from sqlalchemy.ext.declarative import declarative_base
from app.models.sample_result import SampleResult

Base = declarative_base()

class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    task_type = Column(Enum('recall', 'rerank', name='task_type'), default='recall')
    scene_type = Column(String(50), nullable=False)
    sample_set_id = Column(String(255), nullable=False)
    collection_name = Column(String(255), nullable=False)
    top_k = Column(Integer, default=50)
    rerank_k = Column(Integer, default=5)
    status = Column(
        Enum('pending', 'running', 'completed', 'failed', name='task_status'),
        default='pending'
    )
    # 执行结果
    success_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    recall_accuracy_rate = Column(Float, default=0)
    rerank_accuracy_rate = Column(Float, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @classmethod
    def create(cls, db: Session, task: TaskCreate) -> "Task":
        """创建新任务"""
        if not task.name:
            task.name = f"样本测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        db_task = cls(
            name=task.name,
            task_type=task.task_type,
            scene_type=task.scene_type,
            sample_set_id=task.sample_set_id,
            collection_name=task.collection_name,
            top_k=task.top_k,
            rerank_k=task.rerank_k,
            status="pending",
        )
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        return db_task

    @classmethod
    def get_all(cls, db: Session, skip: int = 0, limit: int = 100) -> List["Task"]:
        """获取任务列表"""
        return db.query(cls).offset(skip).limit(limit).all()

    @classmethod
    def get_by_id(cls, db: Session, task_id: int) -> Optional["Task"]:
        """根据ID获取任务"""
        return db.query(cls).filter(cls.id == task_id).first()

    def update(self, db: Session, task_update: TaskUpdate) -> "Task":
        """更新任务"""
        update_data = task_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(self, field, value)
        db.commit()
        db.refresh(self)
        return self

    def delete(self, db: Session) -> bool:
        """删除任务及其关联的样本结果"""
        try:
            # 先删除关联的样本结果
            db.query(SampleResult).filter(
                SampleResult.task_id == self.id
            ).delete()
            
            # 再删除任务本身
            db.delete(self)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error deleting task and its results: {str(e)}")
            raise 