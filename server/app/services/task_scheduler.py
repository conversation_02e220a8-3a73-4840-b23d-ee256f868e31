import asyncio
from sqlalchemy.orm import Session
from app.models.task import Task
from app.services.task_executor import TaskExecutor

class TaskScheduler:
    def __init__(self):
        self.executor = TaskExecutor()
        self._running_tasks = set()

    async def schedule_task(self, db: Session, task: Task) -> None:
        """调度任务执行"""
        print(f"Scheduling task {task.id}")
        if task.id in self._running_tasks:
            print(f"Task {task.id} is already running")
            return

        self._running_tasks.add(task.id)
        print(f"Task {task.id} added to running tasks")
        try:
            await self.executor.execute_task(db, task)
        finally:
            self._running_tasks.remove(task.id)
            print(f"Task {task.id} removed from running tasks")
    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        return task_id in self._running_tasks 