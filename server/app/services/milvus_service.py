import json
from typing import List, Dict
from pymilvus import connections, Collection, utility
from app.schemas.document import NodeInfo
from app.config.config import milvus_uri, dipeak_milvus_collection_name, bj_telecom_milvus_collection_name, cmcc_milvus_collection_name

class MilvusService:
    def __init__(self):
        self.collections = {
            "common": dipeak_milvus_collection_name,
            "bj_telecom": bj_telecom_milvus_collection_name,
            "cmcc": cmcc_milvus_collection_name
        }
        self._ensure_connection()

    def _ensure_connection(self):
        try:
            print(f"Connected to Milvus: {milvus_uri}")
            connections.connect(
                alias="default",
                uri=milvus_uri,
            )
        except Exception as e:
            print(f"Failed to connect to Milvus: {e}")
            raise

    def get_nodes_by_file_id(self, scene_type: str, file_id: str) -> List[NodeInfo]:
        try:
            collection_name = self.collections.get(scene_type)
            if not collection_name:
                raise ValueError(f"Unknown scene type: {scene_type}")

            string_expr = f'file_id == "{file_id}"'
            collection = Collection(collection_name)
            print(f"Querying collection {collection_name} with expr: {string_expr}")
            
            iter = collection.query_iterator(
                batch_size=1000,
                expr=string_expr,
                output_fields=["*"],
            )
            
            nodes = []
            while True:
                try:
                    hits = iter.next()
                    if not hits:
                        break
                    for hit in hits:
                        try:
                            # if hit.get("parent_node", "") != "":
                            #     continue
                            node_content = json.loads(hit["_node_content"])
                            content = node_content["text"]
                            node_id = node_content["id_"]
                            part_name = node_content.get("metadata", {}).get("part_name", "")
                            nodes.append(
                                NodeInfo(
                                    node_id=node_id,
                                    content=content,
                                    part_name=part_name,
                                    text_node=True if hit.get("parent_node", "") == "" else False
                                )
                            )
                        except Exception as e:
                            print(f"Error processing hit: {e}")
                            continue
                except Exception as e:
                    print(f"Error fetching batch: {e}")
                    break

            print(f"Query {string_expr} returned {len(nodes)} nodes")
            return nodes
        except Exception as e:
            print(f"Error in get_nodes_by_file_id: {e}")
            raise

    def get_all_collections(self) -> List[str]:
        """获取所有可用的 collection names"""
        try:
            self._ensure_connection()
            collections = utility.list_collections()
            return collections
        except Exception as e:
            print(f"Failed to get collections: {e}")
            raise