from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, func
from app.models.sample import SampleSetModel, Sample as DBSample, SampleDocument
from app.schemas.document import (
    SampleSet, Sample, SampleCreate, SampleSetCreate,
    DocumentModel, CMCCFileModel, BjTelecomFile,
    SampleSetResponse, SampleResponse
)
from app.services.milvus_service import MilvusService
import json

class SampleService:
    def __init__(self):
        self.milvus_service = MilvusService()

    def _model_to_dict(self, model):
        """Convert SQLAlchemy model to dictionary"""
        return {c.name: getattr(model, c.name) for c in model.__table__.columns}

    def create_sample_set(self, db: Session, data: SampleSetCreate) -> SampleSet:
        sample_set = SampleSetModel(
            name=data.name,
            scene_type=data.scene_type
        )
        db.add(sample_set)
        db.commit()
        db.refresh(sample_set)
        return SampleSet.model_validate(self._model_to_dict(sample_set))

    def get_sample_sets(
        self,
        db: Session,
        scene_type: str,
        skip: int = 0,
        limit: int = 10
    ) -> SampleSetResponse:
        query = select(SampleSetModel).where(SampleSetModel.scene_type == scene_type)
        total = db.scalar(select(func.count()).select_from(query.subquery()))
        items = db.scalars(query.offset(skip).limit(limit)).all()
        
        # 直接构造 SampleSetResponse 对象
        return SampleSetResponse(
            total=total,
            items=[
                SampleSet(
                    id=item.id,
                    name=item.name,
                    scene_type=item.scene_type,
                    created_at=item.created_at,
                    updated_at=item.updated_at
                ) 
                for item in items
            ]
        )

    async def create_sample(self, db: Session, data: SampleCreate) -> Sample:
        # 创建样本记录
        sample = DBSample(
            question=data.question,
            sample_set_id=data.sample_set_id
        )
        db.add(sample)
        db.flush()  # 获取sample.id

        # 处理每个文档
        sample_documents = []
        for doc_data in data.documents:
            # 获取文档节点信息
            nodes = self.milvus_service.get_nodes_by_file_id(data.scene_type, doc_data.document_id)
            node_map = {node.node_id: {"content": node.content, "part_name": node.part_name} for node in nodes}
            
            # 验证并获取预期节点内容
            node_contents = {}
            for node_id in doc_data.expected_node_ids:
                if node_id not in node_map:
                    raise ValueError(f"Node {node_id} not found in document {doc_data.document_id}")
                node_contents[node_id] = node_map[node_id]

            # 创建文档关联记录
            sample_document = SampleDocument(
                sample_id=sample.id,
                document_id=doc_data.document_id,
                document_name=doc_data.document_name,
                expected_node_ids=doc_data.expected_node_ids,
                node_contents=node_contents  # 保存节点内容
            )
            db.add(sample_document)
            sample_documents.append({
                'document_id': doc_data.document_id,
                'document_name': doc_data.document_name,
                'expected_node_ids': doc_data.expected_node_ids,
                'node_contents': node_contents
            })

        db.commit()
        db.refresh(sample)
        
        return Sample(
            id=sample.id,
            question=sample.question,
            documents=sample_documents,
            sample_set_id=sample.sample_set_id,
            created_at=sample.created_at,
            updated_at=sample.updated_at
        )

    def get_samples(
        self,
        db: Session,
        sample_set_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> SampleResponse:
        query = select(DBSample).where(DBSample.sample_set_id == sample_set_id)
        total = db.scalar(select(func.count()).select_from(query.subquery()))
        samples = db.scalars(query.offset(skip).limit(limit)).all()
        
        result = []
        for sample in samples:
            # 获取关联的文档
            documents = db.query(SampleDocument).filter(
                SampleDocument.sample_id == sample.id
            ).all()
            
            # 构造完整的Sample对象，确保documents字段不为None
            result.append(Sample(
                id=sample.id,
                question=sample.question,
                documents=[{
                    'document_id': doc.document_id,
                    'document_name': doc.document_name,
                    'expected_node_ids': doc.expected_node_ids or [],
                    'node_contents': {
                        node_id: (
                            {"content": content, "part_name": ""}
                            if isinstance(content, str)
                            else content
                        )
                        for node_id, content in (doc.node_contents or {}).items()
                    }
                } for doc in documents] or [],
                sample_set_id=sample.sample_set_id,
                created_at=sample.created_at,
                updated_at=sample.updated_at
            ))


        # 返回结果
        return SampleResponse(
            total=total,
            items=result
        )

    def update_sample(self, db: Session, sample_id: str, data: SampleCreate) -> Sample:
        # 使用DBSample获取实例
        sample = db.get(DBSample, sample_id)
        if not sample:
            raise ValueError(f"Sample {sample_id} not found")

        # 更新样本信息（只更新Sample表的字段）
        sample.question = data.question
        # 注意：sample_set_id不应被修改，因为样本属于特定的样本集

        # 删除现有的文档关联
        db.query(SampleDocument).filter(SampleDocument.sample_id == sample_id).delete()

        # 处理新的文档数据
        sample_documents = []
        for doc_data in data.documents:
            # 为每个预期节点创建基本的节点内容结构
            # 这避免了前端访问undefined的错误
            node_contents = {}
            for node_id in doc_data.expected_node_ids:
                node_contents[node_id] = {
                    'content': '',  # 空内容，后续可填充
                    'part_name': ''
                }

            sample_document = SampleDocument(
                sample_id=sample.id,
                document_id=doc_data.document_id,
                document_name=doc_data.document_name,
                expected_node_ids=doc_data.expected_node_ids,
                node_contents=node_contents
            )
            db.add(sample_document)
            sample_documents.append({
                'document_id': doc_data.document_id,
                'document_name': doc_data.document_name,
                'expected_node_ids': doc_data.expected_node_ids,
                'node_contents': node_contents
            })

        db.commit()
        db.refresh(sample)

        return Sample(
            id=sample.id,
            question=sample.question,
            documents=sample_documents,
            sample_set_id=sample.sample_set_id,
            created_at=sample.created_at,
            updated_at=sample.updated_at
        )

    def delete_sample(self, db: Session, sample_id: str):
        # 使用DBSample获取实例
        sample = db.get(DBSample, sample_id)
        if not sample:
            raise ValueError(f"Sample {sample_id} not found")
            
        db.delete(sample)
        db.commit() 