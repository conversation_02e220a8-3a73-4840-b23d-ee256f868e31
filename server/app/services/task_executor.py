import json
from typing import List, Dict, Any
import asyncio
from datetime import datetime
import requests
from sqlalchemy.orm import Session
from app.models.task import Task
from app.schemas.document import Sample
from app.models.sample_result import SampleResult
from app.services.sample_service import SampleService
from sqlalchemy import select
from app.config.config import evaluate_biz_service_addr
from app.schemas.task import RetrieveTaskResultResponse, RetrieveTaskResultData, NodeWithScore, SampleTestResult

class TaskExecutor:
    def __init__(self):
        self.sample_service = SampleService()

    async def _process_sample(self, sample: Sample, task: Task) -> SampleTestResult:
        """处理单个样本的测试"""
        result = SampleTestResult(
            exec_status='success',
            exec_msg=None,
            recall_accuracy=False,
            rerank_accuracy=False,
            original_data=None,
            question=sample.question,
        )

        try:
            msg, data = self.retrieve_nodes(sample, task)
            if data is None:
                result.exec_status = 'failed'
                result.exec_msg = msg
                return result
            result.original_data = data
            # 召回准确率
            result.recall_accuracy = self.check_recall_accuracy(data, sample)
            # 重排准确率
            result.rerank_accuracy = self.check_rerank_accuracy(data, sample)
            return result
        except Exception as e:
            print(f"Sample processing error: {str(e)}")
            result.exec_status = 'failed'
            result.exec_msg = str(e)
            return result

    def check_recall_accuracy(self, data: RetrieveTaskResultData, sample: Sample) -> bool:
        if data.retrieve_results is None:
            return False
        expected_node_ids = []
        for doc in sample.documents:
            expected_node_ids.extend(doc.expected_node_ids)
        retrieved_node_ids = [node.id for node in data.retrieve_results]
        return set(expected_node_ids).issubset(set(retrieved_node_ids))

    def check_rerank_accuracy(self, data: RetrieveTaskResultData, sample: Sample) -> bool:
        if data.rerank_results is None:
            return False
        expected_node_ids = []
        for doc in sample.documents:
            expected_node_ids.extend(doc.expected_node_ids)
        reranked_node_ids = [node.id for node in data.rerank_results]
        return set(expected_node_ids).issubset(set(reranked_node_ids))

    async def execute_task(self, db: Session, task: Task) -> None:
        """执行任务"""
        try:
            # 使用行级锁重新获取任务对象
            locked_task = db.execute(
                select(Task)
                .where(Task.id == task.id)
                .with_for_update()
            ).scalar_one()
            
            # 更新任务状态为运行中
            locked_task.status = "running"
            locked_task.updated_at = datetime.utcnow()
            db.commit()

            # 使用新会话获取样本
            with db.begin_nested():
                samples_response = self.sample_service.get_samples(
                    db, 
                    sample_set_id=locked_task.sample_set_id,
                    skip=0,
                    limit=1000
                )
                samples = samples_response.items

            # 使用异步方式处理所有样本
            results = await asyncio.gather(
                *(self._process_sample(sample, task) for sample in samples),
                return_exceptions=True
            )
            # 处理完成，记录日志
            print(f"Task {task.id}: Processed {len(results)} samples")

            # 保存样本结果
            success_count = 0
            failed_count = 0
            recall_accuracy_count = 0
            rerank_accuracy_count = 0
            for sample, result in zip(samples, results):
                if not isinstance(result, Exception):
                    sample_result = SampleResult(
                        task_id=task.id,
                        sample_id=sample.id,
                        question=sample.question,
                        original_data=json.dumps(result.original_data.model_dump(), ensure_ascii=False) if result.original_data else None,
                        exec_status=result.exec_status,
                        exec_msg=result.exec_msg,
                        recall_accuracy=result.recall_accuracy,
                        rerank_accuracy=result.rerank_accuracy
                    )
                    db.add(sample_result)
                    # 记录样本处理结果
                    if result.exec_status == 'failed':
                        print(f"Task {task.id}: Sample {sample.id} failed - {result.exec_msg}")
                    if result.exec_status == 'success':
                        success_count += 1
                    else:
                        failed_count += 1
                    if result.recall_accuracy:
                        recall_accuracy_count += 1
                    if result.rerank_accuracy:
                        rerank_accuracy_count += 1

            # 更新最终状态前重新关联对象
            db.add(locked_task)
            locked_task.status = "completed"
            locked_task.success_count = success_count
            locked_task.failed_count = failed_count
            locked_task.recall_accuracy_rate = recall_accuracy_count / (success_count + failed_count)
            locked_task.rerank_accuracy_rate = rerank_accuracy_count / (success_count + failed_count)
            db.commit()

        except Exception as e:
            print(f"Task {task.id} execution failed: {str(e)}")
            db.rollback()
            db.add(task)
            task.status = "failed"
            db.commit()
            
        finally:
            # 确保状态同步
            db.expire_all()
            final_state = db.query(Task).get(task.id)
            print(f"Task {task.id} final status: {final_state.status}")

    def retrieve_nodes(self, sample: Sample, task: Task) -> tuple[str, RetrieveTaskResultData]:
        """检索文档节点"""
        try:
            # 构建请求参数
            request_data = {
                "question": sample.question,
                "collection_name": task.collection_name,
                "doc_ids": [],
                "top_k": task.top_k,
                "rerank_k": task.rerank_k
            }

            # 调用检索接口
            response = requests.post(
                f"{evaluate_biz_service_addr}/api/eval/retrieve/search",
                json=request_data,
                timeout=30
            )
            response.raise_for_status()
            
            # 解析响应结果
            result = RetrieveTaskResultResponse.model_validate(response.json())
            if result.code != 0:
                print(f"Task {task.id} retrieve failed: {result.msg}")
                return result.msg, None
            
            return result.msg, result.data
        except Exception as e:
            print(f"Node retrieval error for task {task.id}: {str(e)}")
            raise