import os

# 数据库连接配置
DB_CONFIGS = {
    "biz": os.getenv("BIZ_DB_URL", "mysql+pymysql://root:123@**************:3306/askdoc_test"),
    "common": os.getenv("COMMON_DB_URL", "mysql+pymysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/askdoc"),
    "bj_telecom": os.getenv("BJ_TELECOM_DB_URL", "mysql+pymysql://root:123@**************:3306/askdoc_test"),
    "cmcc": os.getenv("CMCC_DB_URL", "mysql+pymysql://root:123@**************:3306/askdoc_test")
}

# Milvus 配置
milvus_uri = os.getenv("MILVUS_URI", "http://**************:31870")
dipeak_milvus_collection_name = os.getenv("DIPeak_MILVUS_COLLECTION_NAME", "askdocpre_new")
bj_telecom_milvus_collection_name = os.getenv("BJ_TELECOM_MILVUS_COLLECTION_NAME", "xxx_new")
cmcc_milvus_collection_name = os.getenv("CMCC_MILVUS_COLLECTION_NAME", "AskDoc")

# s3 配置
s3_bucket_name = os.getenv("S3_BUCKET_NAME", "ask-doc")
s3_access_key = os.getenv("S3_ACCESS_KEY", "pxpxMQIu2972J3GGgBlK")
s3_secret_key = os.getenv("S3_SECRET_KEY", "09DvvwWeKaNBHV3ox0E8SN4yRiOcbC6HolqWi5wh")
s3_endpoint_url = os.getenv("S3_ENDPOINT_URL", "http://**************:9000")
s3_region_name = os.getenv("S3_REGION_NAME", "us-east-1")

evaluate_biz_service_addr = os.getenv("EVALUATE_BIZ_SERVICE_ADDR", "http://**************:9599")