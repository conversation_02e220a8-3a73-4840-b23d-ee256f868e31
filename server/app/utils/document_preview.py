import logging
import boto3
from app.config.config import s3_bucket_name, s3_access_key, s3_secret_key, s3_endpoint_url, s3_region_name

logger = logging.getLogger(__name__)

def get_s3_presigned_url(key: str, preview: bool = False) -> str:
    s3_client = boto3.client(
        's3',
        endpoint_url=s3_endpoint_url,
        aws_access_key_id=s3_access_key,
        aws_secret_access_key=s3_secret_key,
        region_name=s3_region_name
    )
    
    params = {
        'Bucket': s3_bucket_name,
        'Key': key,
    }
    
    if preview:
        # 添加响应头参数强制预览
        params['ResponseContentDisposition'] = 'inline'
        params['ResponseContentType'] = 'application/pdf'
    
    return s3_client.generate_presigned_url('get_object', Params=params, ExpiresIn=3600)

def get_document_preview_url(scene_type: str, document_id: str, document_name: str, preview: bool = True) -> str:
    document_base_name = document_name.split(".")[0]
    file_type = document_name.split(".")[-1]
    
    if scene_type == "common":
        if file_type == "pdf":
            key = f"doc-resource/doc_{document_id}/{document_name}"
        else:
            key = f"doc-resource/doc_{document_id}/{document_base_name}.pdf"
        return get_s3_presigned_url(key, preview=preview)
    else:
        # http://192.168.111.51:8080/download?FileID=ddfe1176-8839-48eb-a6e7-0a1b94ab246c-pdf&FileName=%E4%BA%91%E8%BF%B9%E5%BC%95%E5%85%A5%E8%B5%84%E6%96%99-%E6%99%BA%E8%83%BD%E6%9C%8D%E5%8A%A1.pdf
        return f"http://192.168.111.51:8080/download?FileID={document_id}-pdf&FileName={document_base_name}.pdf&preview=1"
