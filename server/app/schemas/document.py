from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union, Sequence, Literal

from pydantic import BaseModel, ConfigDict, Field
from sqlalchemy import (
    DateTime,
    Integer,
    Text,
    UniqueConstraint,
    create_engine,
    func,
    select,
    update,
    delete,
    JSON,
    text,
    Boolean,
    Enum,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class CommonDocumentModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    # id for document and file_id for cmcc_files
    id: str
    # source_url for document and file_id for cmcc_files
    file_name: Optional[str]
    file_status: Optional[int] = -1
    source_url: Optional[str] = ""
    file_type: Optional[str] = ""
    # meeting personal temp
    upload_type: Optional[str] = ""
    platform: Optional[str] = ""
    x_location: Optional[str] = ""  # eb多环境
    # for index meta info, eg: {"file_id": "xxx", "meeting_id": "xxx"}
    meta_info: Optional[Dict] = {}
    upload_status: Optional[str] = ""
    app_id: Optional[str] = ""
    # 目录层级支持
    is_dir: Optional[bool] = False
    parent_id: Optional[int] = 0


class DocumentModel(Base):
    __tablename__ = "document"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(256))
    size: Mapped[int] = mapped_column(Integer, nullable=True, default=0)
    mime_type: Mapped[str] = mapped_column(String(64), nullable=True, default="")
    thumbnail_path: Mapped[str] = mapped_column(String(512), nullable=True, default="")
    source_path: Mapped[str] = mapped_column(String(512), nullable=True, default="")
    gmt_created: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    gmt_modified: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False
    )
    file_type: Mapped[str] = mapped_column(String(64), nullable=True, default="")
    language: Mapped[str] = mapped_column(String(64))
    overview: Mapped[str] = mapped_column(Text, nullable=True, default="")
    file_status: Mapped[int] = mapped_column(Integer)
    is_dir: Mapped[bool] = mapped_column(Boolean, default=False)
    parent_id: Mapped[int] = mapped_column(Integer, nullable=True, default=0)


class CMCCFileModel(Base):
    __tablename__ = "cmcc_files"
    id: Mapped[int] = mapped_column(primary_key=True)
    file_id: Mapped[str] = mapped_column(String(256))
    file_name: Mapped[str] = mapped_column(String(256))
    user_id: Mapped[str] = mapped_column(String(256))
    meeting_id: Mapped[str] = mapped_column(String(256))
    file_type: Mapped[str] = mapped_column(String(64))
    upload_status: Mapped[str] = mapped_column(String(64))
    upload_type: Mapped[str] = mapped_column(String(64))
    platform: Mapped[str] = mapped_column(String(64))
    parse_result: Mapped[str] = mapped_column(String(128))
    parse_error_msg: Mapped[str] = mapped_column(String(2048))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    app_id: Mapped[str] = mapped_column(String(64))
    summary: Mapped[str] = mapped_column(Text, nullable=True)
    x_location: Mapped[str] = mapped_column(String(256), nullable=True)


class BjTelecomFile(Base):
    __tablename__ = "bj_telecom_files"

    file_id: Mapped[str] = mapped_column(String(64), primary_key=True)
    resource_id: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    resource_type: Mapped[str] = mapped_column(String(50), nullable=False, default="")
    file_name: Mapped[str] = mapped_column(String(255), nullable=False)
    file_type: Mapped[str] = mapped_column(
        String(64), nullable=False, comment="文件类型"
    )
    upload_status: Mapped[str] = mapped_column(
        Enum(
            "UPLOAD_COMPLETED",
            "INDEX_BUILDING",
            "INDEX_BUILD_FAILED",
            "INDEX_BUILD_SUCCESS",
            "INDEX_BUILD_RETRY",
        ),
        nullable=False,
        default="UPLOAD_COMPLETED",
        comment="文件状态（上传完毕，索引构建中，索引构建失败，索引构建成功, 等待重新构建）",
    )
    removed: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="是否已下架"
    )
    meta_info: Mapped[dict] = mapped_column(
        JSON, nullable=True, comment="文件的元信息，存储为JSON格式"
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=func.now(),
        nullable=False,
        comment="记录创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="记录最后修改时间",
    )

    __table_args__ = (
        UniqueConstraint(
            "resource_id", "resource_type", "file_name", name="uq_resource_file"
        ),
    )


class UploadStatus:
    UPLOAD_IN_PROGRESS = "UPLOAD_IN_PROGRESS"
    UPLOAD_FAILED = "UPLOAD_FAILED"
    UPLOAD_COMPLETED = "UPLOAD_COMPLETED"
    INDEX_BUILDING = "INDEX_BUILDING"
    INDEX_BUILD_FAILED = "INDEX_BUILD_FAILED"
    INDEX_BUILD_SUCCESS = "INDEX_BUILD_SUCCESS"
    DELETED = "DELETED"
    INDEX_BUILD_RETRY = "INDEX_BUILD_RETRY"


class SceneType(str, Enum):
    COMMON = "common"
    BJ_TELECOM = "bj_telecom"
    CMCC = "cmcc"


class DocumentQueryParams(BaseModel):
    scene_type: Literal["common", "bj_telecom", "cmcc"]
    keyword: Optional[str] = None
    page: int = 1
    page_size: int = 10


class PaginatedResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    total: int
    items: List[CommonDocumentModel]


class NodeQueryParams(BaseModel):
    scene_type: Literal["common", "bj_telecom", "cmcc"]
    file_id: str


class NodeInfo(BaseModel):
    node_id: str
    content: str
    part_name: str = ""
    text_node: bool = False


class NodeQueryResponse(BaseModel):
    nodes: List[NodeInfo]


class SampleSet(BaseModel):
    id: str
    name: str
    scene_type: str
    created_at: datetime
    updated_at: datetime


class SampleNode(BaseModel):
    node_id: str
    content: str
    part_name: Optional[str] = ""


class SampleDocument(BaseModel):
    document_id: str
    document_name: str
    expected_node_ids: List[str]
    node_contents: Dict[str, Union[str, Dict[str, str]]]

    @property
    def normalized_node_contents(self) -> Dict[str, Dict[str, str]]:
        """将旧格式的node_contents转换为新格式"""
        normalized = {}
        for node_id, content in self.node_contents.items():
            if isinstance(content, str):
                normalized[node_id] = {"content": content, "part_name": ""}
            else:
                normalized[node_id] = content
        return normalized


class Sample(BaseModel):
    id: str
    question: str
    documents: List[SampleDocument]
    sample_set_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SampleSetCreate(BaseModel):
    name: str
    scene_type: str


class SampleDocumentCreate(BaseModel):
    document_id: str = Field(..., alias="documentId")
    document_name: str = Field(..., alias="documentName")
    expected_node_ids: List[str] = Field(..., alias="expectedNodeIds")

    class Config:
        populate_by_name = True
        allow_population_by_field_name = True


class SampleCreate(BaseModel):
    question: str
    documents: List[SampleDocumentCreate]
    sample_set_id: str = Field(..., alias="sampleSetId")
    scene_type: str = Field(..., alias="sceneType")

    class Config:
        populate_by_name = True
        allow_population_by_field_name = True


class SampleSetResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    total: int
    items: List[SampleSet] = []


class SampleResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    total: int
    items: List[Sample] = []
