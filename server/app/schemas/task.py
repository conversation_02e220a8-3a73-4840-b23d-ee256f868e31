from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime

class TaskBase(BaseModel):
    name: Optional[str] = None
    task_type: Literal['recall', 'rerank'] = 'recall'
    scene_type: str
    sample_set_id: str
    collection_name: str
    top_k: Optional[int] = Field(default=50, ge=1)
    rerank_k: Optional[int] = Field(default=5, ge=1, le=10)
    status: Literal['pending', 'running', 'completed', 'failed'] = 'pending'
    success_count: Optional[int] = Field(default=0)
    failed_count: Optional[int] = Field(default=0)
    recall_accuracy_rate: Optional[float] = Field(default=0)
    rerank_accuracy_rate: Optional[float] = Field(default=0)

class TaskCreate(TaskBase):
    pass

class TaskUpdate(TaskBase):
    status: Optional[str] = None

class TaskInDB(TaskBase):
    id: int
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class Task(TaskInDB):
    pass 

class NodeWithScore(BaseModel):
    id: str
    text: str
    score: float

class RetrieveTaskResultData(BaseModel):
    retrieve_results: Optional[List[NodeWithScore]] = None
    rerank_results: Optional[List[NodeWithScore]] = None

class RetrieveTaskResultResponse(BaseModel):
    code: int
    msg: str
    data: RetrieveTaskResultData

class SampleTestResult(BaseModel):
    exec_status: Literal['success', 'failed']
    exec_msg: Optional[str] = None
    #召回准确与否
    recall_accuracy: Optional[bool] = None
    #重排准确与否
    rerank_accuracy: Optional[bool] = None
    original_data: Optional[Dict[str, Any]] = None
    question: str
