/* 现代化全局样式 - 参考 GitHub/Linear/Notion 设计系统 */

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f6f8fa;
  color: #1f2328;
  line-height: 1.5;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #d1d9e0;
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #8c959f;
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 现代化表格样式 */
.ant-table {
  border-radius: 8px !important;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06) !important;
  border: 1px solid #d1d9e0 !important;
}

.ant-table-thead > tr > th {
  background: #f6f8fa !important;
  border-bottom: 1px solid #d1d9e0 !important;
  color: #1f2328 !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  padding: 12px 16px !important;
  position: relative;
}

.ant-table-thead > tr > th::after {
  display: none !important;
}

.ant-table-tbody > tr {
  transition: all 0.15s ease;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid #eaeef2 !important;
  padding: 12px 16px !important;
  color: #1f2328;
  font-size: 14px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f6f8fa !important;
}

.ant-table-tbody > tr:last-child > td {
  border-bottom: none !important;
}

/* 文件夹行现代化样式 */
.folder-row {
  background: linear-gradient(90deg, rgba(9, 105, 218, 0.03) 0%, transparent 100%) !important;
  border-left: 3px solid #0969da !important;
}

.folder-row:hover {
  background: linear-gradient(90deg, rgba(9, 105, 218, 0.06) 0%, transparent 100%) !important;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(16, 24, 40, 0.1) !important;
}

/* 现代化卡片样式 */
.ant-card {
  border: 1px solid #d1d9e0 !important;
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff !important;
}

.ant-card:hover {
  box-shadow: 0 4px 6px -1px rgba(16, 24, 40, 0.1), 0 2px 4px -1px rgba(16, 24, 40, 0.06) !important;
  transform: translateY(-1px);
  border-color: #8c959f !important;
}

.ant-card-head {
  border-bottom: 1px solid #eaeef2 !important;
  padding: 16px 24px !important;
  background: #f6f8fa !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-card-head-title {
  color: #1f2328 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

.ant-card-body {
  padding: 24px !important;
}

/* 现代化按钮样式 */
.ant-btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  height: 36px !important;
  padding: 0 16px !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #d1d9e0 !important;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05) !important;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(16, 24, 40, 0.1) !important;
}

.ant-btn-primary {
  background: #0969da !important;
  border-color: #0969da !important;
  color: #ffffff !important;
}

.ant-btn-primary:hover {
  background: #0860ca !important;
  border-color: #0860ca !important;
  box-shadow: 0 2px 8px rgba(9, 105, 218, 0.25) !important;
}

.ant-btn-text {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  color: #656d76 !important;
}

.ant-btn-text:hover {
  background: #f6f8fa !important;
  color: #1f2328 !important;
  transform: none;
  box-shadow: none !important;
}

.ant-btn-dangerous {
  color: #cf222e !important;
  border-color: #cf222e !important;
}

.ant-btn-dangerous:hover {
  background: #cf222e !important;
  border-color: #cf222e !important;
  color: #ffffff !important;
}

/* 面包屑悬停效果 */
.ant-breadcrumb-link:hover {
  color: #1677ff !important;
  background-color: #f0f5ff;
  border-radius: 4px;
  padding: 2px 6px;
  margin: -2px -6px;
  transition: all 0.2s ease;
}

/* 标签样式优化 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 输入框聚焦效果 */
.ant-input:focus,
.ant-input-focused {
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 选择器聚焦效果 */
.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #1677ff;
}

.ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1) !important;
}

/* 模态框样式 */
.ant-modal {
  border-radius: 12px;
  overflow: hidden;
}

.ant-modal-header {
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ant-modal-title {
  color: white !important;
  font-weight: 600;
}

.ant-modal-close {
  color: white !important;
}

.ant-modal-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 加载状态样式 */
.ant-spin-dot-item {
  background-color: #1677ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px !important;
  }
  
  .ant-card {
    margin: 8px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px !important;
  }
  
  .ant-btn {
    font-size: 12px;
    height: 28px;
    padding: 0 8px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* 页面加载动画 */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 表格头部样式 */
.ant-table-thead > tr > th {
  background: #fafafa !important;
  font-weight: 600 !important;
  color: #262626 !important;
  border-bottom: 2px solid #e8e8e8 !important;
  position: relative;
}

.ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1677ff, #722ed1);
}

/* 分段控制器样式 */
.ant-segmented {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 2px;
}

.ant-segmented-item-selected {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}
/* 
现代化输入框和选择器 */
.ant-input,
.ant-select-selector {
  border: 1px solid #d1d9e0 !important;
  border-radius: 6px !important;
  background: #ffffff !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px !important;
}

.ant-input:hover,
.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #8c959f !important;
}

.ant-input:focus,
.ant-input-focused,
.ant-select-focused .ant-select-selector {
  border-color: #0969da !important;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1) !important;
}

/* 现代化标签 */
.ant-tag {
  border-radius: 6px !important;
  border: 1px solid #d1d9e0 !important;
  background: #f6f8fa !important;
  color: #1f2328 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  padding: 2px 8px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 4px !important;
  transition: all 0.15s ease;
}

.ant-tag-success {
  background: #dafbe1 !important;
  border-color: #1a7f37 !important;
  color: #1a7f37 !important;
}

.ant-tag-processing {
  background: #ddf4ff !important;
  border-color: #0969da !important;
  color: #0969da !important;
}

.ant-tag-error {
  background: #ffebe9 !important;
  border-color: #cf222e !important;
  color: #cf222e !important;
}

.ant-tag-warning {
  background: #fff8c5 !important;
  border-color: #d1242f !important;
  color: #d1242f !important;
}

/* 现代化分段控制器 */
.ant-segmented {
  background: #f6f8fa !important;
  border: 1px solid #d1d9e0 !important;
  border-radius: 8px !important;
  padding: 2px !important;
}

.ant-segmented-item {
  border-radius: 6px !important;
  color: #656d76 !important;
  font-weight: 500 !important;
  transition: all 0.15s ease;
}

.ant-segmented-item:hover {
  color: #1f2328 !important;
}

.ant-segmented-item-selected {
  background: #ffffff !important;
  color: #1f2328 !important;
  box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06) !important;
  font-weight: 600 !important;
}

/* 现代化模态框 */
.ant-modal {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.05) !important;
}

.ant-modal-header {
  background: #f6f8fa !important;
  border-bottom: 1px solid #d1d9e0 !important;
  padding: 20px 24px !important;
}

.ant-modal-title {
  color: #1f2328 !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.ant-modal-body {
  padding: 24px !important;
}

.ant-modal-footer {
  border-top: 1px solid #eaeef2 !important;
  padding: 16px 24px !important;
  background: #f6f8fa !important;
}

/* 现代化消息提示 */
.ant-message {
  top: 24px !important;
}

.ant-message-notice {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(16, 24, 40, 0.1), 0 2px 4px -1px rgba(16, 24, 40, 0.06) !important;
  border: 1px solid #d1d9e0 !important;
}

.ant-message-success .ant-message-notice {
  background: #dafbe1 !important;
  border-color: #1a7f37 !important;
}

.ant-message-error .ant-message-notice {
  background: #ffebe9 !important;
  border-color: #cf222e !important;
}

.ant-message-warning .ant-message-notice {
  background: #fff8c5 !important;
  border-color: #d1242f !important;
}

.ant-message-info .ant-message-notice {
  background: #ddf4ff !important;
  border-color: #0969da !important;
}

/* 现代化工具提示 */
.ant-tooltip {
  font-size: 12px !important;
}

.ant-tooltip-inner {
  background: #1f2328 !important;
  border-radius: 6px !important;
  padding: 6px 10px !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 6px -1px rgba(16, 24, 40, 0.1), 0 2px 4px -1px rgba(16, 24, 40, 0.06) !important;
}

.ant-tooltip-arrow::before {
  background: #1f2328 !important;
}

/* 现代化加载状态 */
.ant-spin-dot-item {
  background-color: #0969da !important;
}

.ant-spin-text {
  color: #656d76 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* 现代化空状态 */
.ant-empty {
  color: #656d76 !important;
}

.ant-empty-description {
  color: #8c959f !important;
  font-size: 14px !important;
}

/* 现代化面包屑 */
.ant-breadcrumb {
  font-size: 14px !important;
}

.ant-breadcrumb-link {
  color: #656d76 !important;
  transition: color 0.15s ease;
}

.ant-breadcrumb-link:hover {
  color: #0969da !important;
}

.ant-breadcrumb-separator {
  color: #8c959f !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px !important;
  }
  
  .ant-card {
    margin: 8px !important;
    border-radius: 8px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }
  
  .ant-btn {
    font-size: 13px !important;
    height: 32px !important;
    padding: 0 12px !important;
  }
  
  .ant-segmented-item {
    padding: 6px 12px !important;
    font-size: 13px !important;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
  animation: fadeIn 0.2s ease;
}

/* 现代化布局容器 */
.modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 现代化页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 0;
  margin-bottom: 32px;
  border-radius: 12px;
}

.page-header h1 {
  color: white !important;
  margin: 0 !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  letter-spacing: -0.025em;
}

.page-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  margin: 8px 0 0 0 !important;
  font-size: 16px !important;
}/* 现代化
导航栏样式 */
.modern-navbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1px solid rgba(209, 217, 224, 0.8) !important;
}

.navbar-logo {
  transition: transform 0.2s ease;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

.navbar-nav-item {
  position: relative;
  overflow: hidden;
}

.navbar-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(9, 105, 218, 0.1), transparent);
  transition: left 0.5s ease;
}

.navbar-nav-item:hover::before {
  left: 100%;
}

.navbar-user-menu {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(209, 217, 224, 0.8) !important;
  box-shadow: 0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.05) !important;
}

/* 导航栏响应式设计 */
@media (max-width: 1024px) {
  .ant-layout-header {
    padding: 0 16px !important;
  }
  
  .navbar-nav-item span:last-child {
    display: none;
  }
  
  .navbar-nav-item {
    padding: 8px 12px !important;
  }
}

@media (max-width: 768px) {
  .navbar-logo div:last-child {
    display: none;
  }
  
  .navbar-user-info {
    display: none;
  }
}

/* 导航栏动画效果 */
@keyframes navSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.navbar-item-enter {
  animation: navSlideIn 0.3s ease-out;
}

/* 活跃导航项的特殊效果 */
.navbar-nav-item.active {
  position: relative;
}

.navbar-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: linear-gradient(90deg, #0969da, #8250df);
  border-radius: 1px;
  animation: activeIndicator 0.3s ease-out;
}

@keyframes activeIndicator {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 20px;
    opacity: 1;
  }
}

/* 通知徽章样式 */
.ant-badge-count {
  background: #cf222e !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px rgba(207, 34, 46, 0.3) !important;
  font-size: 10px !important;
  font-weight: 600 !important;
  min-width: 16px !important;
  height: 16px !important;
  line-height: 12px !important;
}

/* 用户头像样式 */
.ant-avatar {
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 2px 8px rgba(16, 24, 40, 0.15) !important;
  transition: all 0.2s ease;
}

.ant-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(16, 24, 40, 0.2) !important;
}

/* 下拉菜单样式优化 */
.ant-dropdown {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.05) !important;
}

.ant-dropdown-menu {
  border-radius: 12px !important;
  border: 1px solid #d1d9e0 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 8px !important;
}

.ant-dropdown-menu-item {
  border-radius: 6px !important;
  margin: 2px 0 !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.15s ease;
}

.ant-dropdown-menu-item:hover {
  background: #f6f8fa !important;
  color: #1f2328 !important;
}

.ant-dropdown-menu-item-danger:hover {
  background: #ffebe9 !important;
  color: #cf222e !important;
}

.ant-dropdown-menu-item-divider {
  margin: 8px 0 !important;
  background: #eaeef2 !important;
}

/* Logo 特殊效果 */
.logo-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 导航栏毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(209, 217, 224, 0.3);
}

/* 现代化阴影层次 */
.shadow-sm {
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(16, 24, 40, 0.1), 0 2px 4px -1px rgba(16, 24, 40, 0.06);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.05);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(16, 24, 40, 0.1), 0 10px 10px -5px rgba(16, 24, 40, 0.04);
}/* 
现代化分页器样式 */
.ant-pagination {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  flex-wrap: wrap !important;
  gap: 16px !important;
}

.ant-pagination-total-text {
  color: #656d76 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin: 0 !important;
}

.ant-pagination-item {
  border: 1px solid #d1d9e0 !important;
  border-radius: 6px !important;
  background: #ffffff !important;
  color: #1f2328 !important;
  font-weight: 500 !important;
  transition: all 0.15s ease !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

.ant-pagination-item:hover {
  border-color: #0969da !important;
  color: #0969da !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(9, 105, 218, 0.15) !important;
}

.ant-pagination-item-active {
  background: #0969da !important;
  border-color: #0969da !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(9, 105, 218, 0.25) !important;
}

.ant-pagination-item-active:hover {
  background: #0860ca !important;
  border-color: #0860ca !important;
  transform: translateY(-1px) !important;
}

.ant-pagination-prev,
.ant-pagination-next {
  border: 1px solid #d1d9e0 !important;
  border-radius: 6px !important;
  background: #ffffff !important;
  color: #656d76 !important;
  transition: all 0.15s ease !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

.ant-pagination-prev:hover,
.ant-pagination-next:hover {
  border-color: #0969da !important;
  color: #0969da !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(9, 105, 218, 0.15) !important;
}

.ant-pagination-disabled {
  opacity: 0.4 !important;
  cursor: not-allowed !important;
}

.ant-pagination-disabled:hover {
  border-color: #d1d9e0 !important;
  color: #8c959f !important;
  transform: none !important;
  box-shadow: none !important;
}

.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  border: 1px solid #d1d9e0 !important;
  border-radius: 6px !important;
  background: #ffffff !important;
  color: #8c959f !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

.ant-pagination-jump-prev:hover,
.ant-pagination-jump-next:hover {
  border-color: #0969da !important;
  color: #0969da !important;
}

/* 页面大小选择器样式 */
.ant-pagination-options {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.ant-pagination-options-size-changer {
  margin: 0 !important;
}

.ant-pagination-options-size-changer .ant-select {
  min-width: 80px !important;
}

.ant-pagination-options-size-changer .ant-select-selector {
  border-radius: 6px !important;
  height: 32px !important;
  border: 1px solid #d1d9e0 !important;
  background: #ffffff !important;
}

.ant-pagination-options-size-changer .ant-select-selector:hover {
  border-color: #0969da !important;
}

.ant-pagination-options-size-changer .ant-select-focused .ant-select-selector {
  border-color: #0969da !important;
  box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.1) !important;
}

/* 快速跳转输入框样式 */
.ant-pagination-options-quick-jumper {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #656d76 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.ant-pagination-options-quick-jumper input {
  width: 60px !important;
  height: 32px !important;
  border-radius: 6px !important;
  border: 1px solid #d1d9e0 !important;
  text-align: center !important;
  font-weight: 500 !important;
}

.ant-pagination-options-quick-jumper input:hover {
  border-color: #0969da !important;
}

.ant-pagination-options-quick-jumper input:focus {
  border-color: #0969da !important;
  box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.1) !important;
}

/* 分页器容器样式 */
.ant-table-pagination.ant-pagination {
  margin: 0 !important;
  padding: 16px 24px !important;
  background: #f6f8fa !important;
  border-top: 1px solid #d1d9e0 !important;
  border-radius: 0 0 12px 12px !important;
}

/* 响应式分页器 */
@media (max-width: 768px) {
  .ant-pagination {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 12px !important;
  }
  
  .ant-pagination-total-text {
    text-align: center !important;
    order: -1 !important;
  }
  
  .ant-pagination-options {
    justify-content: center !important;
    order: 1 !important;
  }
  
  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next {
    min-width: 28px !important;
    height: 28px !important;
    line-height: 26px !important;
    font-size: 12px !important;
  }
}

/* 分页器动画效果 */
.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next {
  position: relative;
  overflow: hidden;
}

.ant-pagination-item::before,
.ant-pagination-prev::before,
.ant-pagination-next::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(9, 105, 218, 0.1), transparent);
  transition: left 0.5s ease;
}

.ant-pagination-item:hover::before,
.ant-pagination-prev:hover::before,
.ant-pagination-next:hover::before {
  left: 100%;
}

.ant-pagination-item-active::before {
  display: none;
}