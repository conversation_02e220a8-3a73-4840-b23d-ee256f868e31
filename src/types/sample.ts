export interface Sample {
  id: string;
  question: string;
  expectedNodeIds: string[];
  documentId: string;
}

export interface SampleSet {
  id: string;
  name: string;
  samples: Sample[];
}

export interface Scene {
  id: string;
  name: string;
  sampleSets: SampleSet[];
}

export enum SceneType {
  COMMON = "common",
  BJ_TELECOM = "bj_telecom",
  CMCC = "cmcc"
}

export const SceneTypeLabels = {
  [SceneType.COMMON]: "通用场景",
  [SceneType.BJ_TELECOM]: "北京电信",
  [SceneType.CMCC]: "移动场景"
}; 