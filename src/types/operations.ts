export interface FormValues {
  name?: string;
  question?: string;
  expectedNodeIds?: string[];
  documentId?: string;
  document_id?: string;
  scene_type?: string;
  sample_set_id?: string;
  id?: string;
  created_at?: string;
  updated_at?: string;
  documents?: Array<{
    documentId: string;
    documentName: string;
    expectedNodeIds: string[];
  }>;
}

export type OperationType = 'add' | 'edit' | 'delete';
export type EntityType = 'scene' | 'sampleSet' | 'sample'; 