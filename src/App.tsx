import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { Layout, ConfigProvider, theme, Avatar, Dropdown, Badge, Button, Typography } from 'antd';
import type { MenuProps } from 'antd';
import { 
  HomeOutlined, 
  FolderOutlined, 
  FileTextOutlined, 
  SearchOutlined, 
  BarChartOutlined, 
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import './styles/global.css';
import Home from './pages/Home';
import NodePreviewTab from './components/NodePreviewTab';
import DocumentPreview from './components/DocumentPreview';
import DocumentManagement from './pages/DocumentManagement';
import SampleManagement from './pages/SampleManagement';
import TaskManagement from './pages/TaskManagement';

const { Header, Content } = Layout;

const { Text } = Typography;

const AppContent: React.FC = () => {
  const location = useLocation();
  const { token } = theme.useToken();

  // 导航项配置
  const navigationItems = [
    {
      key: '/',
      path: '/',
      label: '首页',
      icon: <HomeOutlined />,
    },
    {
      key: '/document-management',
      path: '/document-management',
      label: '文档管理',
      icon: <FolderOutlined />,
    },
    {
      key: '/document-preview',
      path: '/document-preview',
      label: '文档预览',
      icon: <FileTextOutlined />,
    },
    {
      key: '/node-preview',
      path: '/node-preview',
      label: '节点预览',
      icon: <SearchOutlined />,
    },
    {
      key: '/samples',
      path: '/samples',
      label: '样本管理',
      icon: <BarChartOutlined />,
    },
    {
      key: '/tasks',
      path: '/tasks',
      label: '任务管理',
      icon: <SettingOutlined />,
    },
  ];

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 现代化导航栏 */}
      <Header style={{ 
        background: '#ffffff',
        borderBottom: `1px solid ${token.colorBorderSecondary}`,
        padding: '0 32px',
        height: 72,
        display: 'flex', 
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06)',
        zIndex: 1000,
        position: 'sticky',
        top: 0
      }}>
        {/* 左侧：Logo + 导航 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 48 }}>
          {/* Logo */}
          <Link to="/" style={{ textDecoration: 'none' }}>
            <div style={{ 
              display: 'flex',
              alignItems: 'center',
              gap: 12
            }}>
              <div style={{
                width: 40,
                height: 40,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: 10,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: 20,
                boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
              }}>
                📚
              </div>
              <div>
                <div style={{ 
                  fontSize: 18,
                  fontWeight: 700,
                  color: token.colorText,
                  lineHeight: 1,
                  letterSpacing: '-0.02em'
                }}>
                  DocStudio
                </div>
                <div style={{ 
                  fontSize: 11,
                  color: token.colorTextTertiary,
                  lineHeight: 1,
                  marginTop: 2,
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  文档评估工作室
                </div>
              </div>
            </div>
          </Link>

          {/* 主导航 */}
          <nav style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {navigationItems.map((item) => {
              const isActive = location.pathname === item.path || 
                (item.path === '/' && location.pathname === '/home');
              
              return (
                <Link
                  key={item.key}
                  to={item.path}
                  style={{ textDecoration: 'none' }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                    padding: '8px 16px',
                    borderRadius: 8,
                    fontSize: 14,
                    fontWeight: 500,
                    color: isActive ? token.colorPrimary : token.colorTextSecondary,
                    background: isActive ? `${token.colorPrimary}08` : 'transparent',
                    border: isActive ? `1px solid ${token.colorPrimary}20` : '1px solid transparent',
                    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    cursor: 'pointer',
                    position: 'relative'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = token.colorFillSecondary;
                      e.currentTarget.style.color = token.colorText;
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = 'transparent';
                      e.currentTarget.style.color = token.colorTextSecondary;
                    }
                  }}
                  >
                    <span style={{ fontSize: 16 }}>{item.icon}</span>
                    {item.label}
                    {isActive && (
                      <div style={{
                        position: 'absolute',
                        bottom: -9,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        width: 4,
                        height: 4,
                        background: token.colorPrimary,
                        borderRadius: '50%'
                      }} />
                    )}
                  </div>
                </Link>
              );
            })}
          </nav>
        </div>

        {/* 右侧：通知 + 用户信息 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          {/* 通知按钮 */}
          <Badge count={3} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{
                width: 40,
                height: 40,
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: token.colorTextSecondary,
                fontSize: 16
              }}
            />
          </Badge>

          {/* 用户下拉菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow={{ pointAtCenter: true }}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 12,
              padding: '6px 12px',
              borderRadius: 8,
              cursor: 'pointer',
              transition: 'all 0.15s ease',
              border: `1px solid transparent`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = token.colorFillSecondary;
              e.currentTarget.style.borderColor = token.colorBorder;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
              e.currentTarget.style.borderColor = 'transparent';
            }}
            >
              <Avatar
                size={32}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  fontSize: 14,
                  fontWeight: 600
                }}
              >
                U
              </Avatar>
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                <Text style={{ 
                  fontSize: 13, 
                  fontWeight: 600, 
                  color: token.colorText,
                  lineHeight: 1
                }}>
                  用户名
                </Text>
                <Text style={{ 
                  fontSize: 11, 
                  color: token.colorTextTertiary,
                  lineHeight: 1,
                  marginTop: 2
                }}>
                  管理员
                </Text>
              </div>
            </div>
          </Dropdown>
        </div>
      </Header>

      {/* 内容区域 */}
      <Content style={{ 
        background: token.colorBgLayout,
        minHeight: 'calc(100vh - 72px)'
      }}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/home" element={<Home />} />
          <Route path="/document-management" element={<DocumentManagement />} />
          <Route path="/document-preview" element={<DocumentPreview />} />
          <Route path="/node-preview" element={<NodePreviewTab />} />
          <Route path="/samples" element={<SampleManagement />} />
          <Route path="/tasks" element={<TaskManagement />} />
        </Routes>
      </Content>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          // 现代化配色方案 - 参考 GitHub/Linear 设计
          colorPrimary: '#0969da',
          colorSuccess: '#1a7f37',
          colorWarning: '#d1242f',
          colorError: '#cf222e',
          colorInfo: '#0969da',
          colorText: '#1f2328',
          colorTextSecondary: '#656d76',
          colorTextTertiary: '#8c959f',
          colorBgBase: '#ffffff',
          colorBgLayout: '#f6f8fa',
          colorBgContainer: '#ffffff',
          colorBgElevated: '#ffffff',
          colorBorder: '#d1d9e0',
          colorBorderSecondary: '#d8dee4',
          colorFill: '#f6f8fa',
          colorFillSecondary: '#eaeef2',
          colorFillTertiary: '#d1d9e0',
          colorFillQuaternary: '#afb8c1',
          
          // 现代化圆角和阴影
          borderRadius: 6,
          borderRadiusLG: 8,
          borderRadiusXS: 4,
          boxShadow: '0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06)',
          boxShadowSecondary: '0 4px 6px -1px rgba(16, 24, 40, 0.1), 0 2px 4px -1px rgba(16, 24, 40, 0.06)',
          boxShadowTertiary: '0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.05)',
          
          // 字体和间距
          fontSize: 14,
          fontSizeLG: 16,
          fontSizeXL: 18,
          lineHeight: 1.5,
          controlHeight: 36,
          controlHeightSM: 28,
          controlHeightLG: 44,
        },
        components: {
          Layout: {
            headerBg: '#ffffff',
            bodyBg: '#f6f8fa',
            headerHeight: 72,
            headerPadding: '0 32px',
          },
          Card: {
            borderRadiusLG: 12,
            paddingLG: 24,
            boxShadowTertiary: '0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06)',
          },
          Table: {
            borderRadiusLG: 8,
            headerBg: '#f6f8fa',
            headerColor: '#1f2328',
            colorBgContainer: '#ffffff',
            borderColor: '#d1d9e0',
            rowHoverBg: '#f6f8fa',
          },
          Button: {
            borderRadius: 6,
            controlHeight: 36,
            fontWeight: 500,
            primaryShadow: '0 1px 2px rgba(16, 24, 40, 0.05)',
          },
          Input: {
            borderRadius: 6,
            controlHeight: 36,
            colorBorder: '#d1d9e0',
            colorBgContainer: '#ffffff',
          },
          Select: {
            borderRadius: 6,
            controlHeight: 36,
            colorBorder: '#d1d9e0',
            colorBgContainer: '#ffffff',
          },
          Menu: {
            itemBg: 'transparent',
            itemSelectedBg: 'rgba(9, 105, 218, 0.1)',
            itemSelectedColor: '#0969da',
            itemHoverBg: 'rgba(255, 255, 255, 0.1)',
            itemHoverColor: '#ffffff',
          },
          Tag: {
            borderRadiusSM: 4,
            fontSizeSM: 12,
            lineHeightSM: 1.4,
          },
          Breadcrumb: {
            fontSize: 14,
            itemColor: '#656d76',
            linkColor: '#656d76',
            linkHoverColor: '#0969da',
            separatorColor: '#8c959f',
          },
        },
      }}
    >
      <Router>
        <AppContent />
      </Router>
    </ConfigProvider>
  );
};

export default App;
