import { API_ENDPOINTS, DOC_PARSER_API_BASE_URL } from '../config';
import { message } from 'antd';

export interface Document {
  id: string;
  file_name: string;
  file_type?: string;
  file_status?: number;
  source_url?: string;
  upload_status?: string;
  scene_type?: string;
  created_at?: string;
  updated_at?: string;
  is_dir?: boolean;
  parent_id?: number;
}

export interface FetchDocumentsParams {
  scene_type: string;
  keyword?: string;
  parent_id?: number;
  page: number;
  page_size: number;
  order_by?: string;
  order?: 'asc' | 'desc';
}

export interface DocumentsResponse {
  total: number;
  items: Document[];
}

export const fetchDocuments = async (params: FetchDocumentsParams): Promise<DocumentsResponse> => {
  const searchParams = new URLSearchParams({
    scene_type: params.scene_type,
    page: String(params.page),
    page_size: String(params.page_size),
  });

  // 只有当keyword有值时才添加到查询参数中
  if (params.keyword) {
    searchParams.append('keyword', params.keyword);
  }

  // 只有当parent_id不为undefined时才添加到查询参数中
  if (params.parent_id !== undefined) {
    searchParams.append('parent_id', String(params.parent_id));
  }

  // 添加排序参数
  if (params.order_by) {
    searchParams.append('order_by', params.order_by);
  }
  if (params.order) {
    searchParams.append('order', params.order);
  }

  const response = await fetch(`${API_ENDPOINTS.documents}/?${searchParams}`);

  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  return response.json();
};

export interface NodeInfo {
  node_id: string;
  content: string;
  part_name: string;
  text_node: boolean;
}

export interface NodeQueryResponse {
  nodes: NodeInfo[];
}

export const fetchDocumentNodes = async (scene_type: string, file_id: string): Promise<NodeQueryResponse> => {
  const response = await fetch(
    `${API_ENDPOINTS.documents}/nodes?${new URLSearchParams({
      scene_type,
      file_id,
    })}`
  );

  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  return response.json();
};

export interface SampleSet {
  id: string;
  name: string;
  scene_type: string;
  collection_name: string;
  created_at: string;
  updated_at: string;
}

export interface SampleNode {
  node_id: string;
  content: string;
}

export interface Sample {
  id: string;
  question: string;
  documents: Array<{
    document_id: string;
    document_name: string;
    expected_node_ids: string[];
    node_contents: { [key: string]: string | { content: string; part_name: string } };
  }>;
  sample_set_id: string;
  created_at: string;
  updated_at: string;
}

export const createSampleSet = async (data: { 
  name: string; 
  scene_type: string;
  collection_name: string;
}): Promise<SampleSet> => {
  const response = await fetch(`${API_ENDPOINTS.samples}/sample-sets`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Network response was not ok');
  return response.json();
};

export const getSampleSets = async (scene_type: string, page: number = 1, page_size: number = 100) => {
  const response = await fetch(
    `${API_ENDPOINTS.samples}/sample-sets?${new URLSearchParams({
      scene_type,
      page: String(page),
      page_size: String(page_size),
    })}`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch sample sets');
  }

  return response.json();
};

export interface SampleDocumentData {
  document_id: string;
  document_name: string;
  expected_node_ids: string[];
}

export const createSample = async (data: {
  question: string;
  documents: SampleDocumentData[];
  sample_set_id: string;
  scene_type: string;
}): Promise<Sample> => {
  const response = await fetch(`${API_ENDPOINTS.samples}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Network response was not ok');
  return response.json();
};

export const getSamples = async (sample_set_id: string, page: number = 1, page_size: number = 10) => {
  const response = await fetch(
    `${API_ENDPOINTS.samples}?${new URLSearchParams({
      sample_set_id,
      page: String(page),
      page_size: String(page_size),
    })}`
  );
  if (!response.ok) throw new Error('Network response was not ok');
  return response.json();
};

export const updateSample = async (sampleId: string, data: {
  question: string;
  document_id?: string; // 保持向后兼容 - 单文档格式
  expected_node_ids?: string[]; // 保持向后兼容
  sample_set_id?: string; // 保持向后兼容
  scene_type?: string; // 保持向后兼容
  document_name?: string; // 保持向后兼容
  documents?: { // 新多文档格式
    documentId: string;
    documentName: string;
    expectedNodeIds: string[];
  }[];
  sampleSetId?: string; // 新字段名（驼峰）
  sceneType?: string; // 新字段名（驼峰）
}): Promise<Sample> => {
  const response = await fetch(`${API_ENDPOINTS.samples}/${sampleId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Network response was not ok');
  return response.json();
};

export const deleteSample = async (sampleId: string): Promise<void> => {
  const response = await fetch(`${API_ENDPOINTS.samples}/${sampleId}`, {
    method: 'DELETE',
  });
  if (!response.ok) throw new Error('Network response was not ok');
};

export const getDocumentPreviewUrl = async (
  documentId: string,
  fileName: string,
  sceneType: string
): Promise<string> => {
  try {
    const response = await fetch(
      `${API_ENDPOINTS.documents}/${documentId}/download-url?${new URLSearchParams({
        scene_type: sceneType,
        file_name: fileName,
      })}`
    );

    if (!response.ok) {
      throw new Error('Failed to get document preview URL');
    }

    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Error fetching document preview URL:', error);
    throw error;
  }
};

export interface NodeUnit {
  text: string;
  metadata: Record<string, any>;
}

export interface PreviewResponse {
  code: number; 
  msg: string;
  data?: NodeUnit[];
}

export interface PreviewParams {
  file: File;
  scene: string;
  platform: string;
  uploadType: string;
  general_parser_chunk_size?: number;
  general_parser_chunk_overlap?: number;
  table_chunk_size?: number;
  table_chunk_overlap?: number;
}

export const previewDocument = async (params: PreviewParams): Promise<PreviewResponse> => {
  const formData = new FormData();
  formData.append('file', params.file);
  formData.append('file_name', params.file.name);
  console.info('file name: ', params.file.name);
  formData.append('scene', params.scene);
  formData.append('platform', params.platform);
  formData.append('upload_type', params.uploadType);
  
  if (params.general_parser_chunk_size) {
    formData.append('chunk_size', String(params.general_parser_chunk_size));
  }
  if (params.general_parser_chunk_overlap) {
    formData.append('chunk_overlap', String(params.general_parser_chunk_overlap));
  }
  if (params.table_chunk_size) {
    formData.append('table_chunk_size', String(params.table_chunk_size));
  }
  if (params.table_chunk_overlap) {
    formData.append('table_chunk_overlap', String(params.table_chunk_overlap));
  }

  const response = await fetch(`${DOC_PARSER_API_BASE_URL}/api/parser/preview`, {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error('预览请求失败');
  }

  return response.json();
};

export interface Task {
  id: string;
  name: string;
  task_type: 'recall' | 'rerank';
  scene_type: string;
  sample_set_id: string;
  collection_name: string;
  top_k: number;
  rerank_k: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  success_count: number;
  failed_count: number;
  recall_accuracy_rate: number;
  rerank_accuracy_rate: number;
  created_at: string;
  updated_at: string;
}

export interface SampleResult {
  exec_status: 'success' | 'failed';
  exec_msg?: string;
  id: number;
  
  recall_accuracy: boolean;
  rerank_accuracy: boolean;
  original_data: any;
  created_at: string;
}

export interface TaskResult {
  items: SampleResult[];
  total: number;
}

// 统一的请求处理函数
const request = async (url: string, options?: RequestInit) => {
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    message.error('请求失败：' + (error instanceof Error ? error.message : '未知错误'));
    throw error;
  }
};

// 任务管理相关的API
export const taskApi = {
  // 获取任务列表
  getTasks: async (): Promise<Task[]> => {
    return request(API_ENDPOINTS.tasks);
  },

  // 获取单个任务
  getTask: async (id: string): Promise<Task> => {
    return request(`${API_ENDPOINTS.tasks}/${id}`);
  },

  // 创建任务
  createTask: async (params: CreateTaskParams): Promise<void> => {
    return request(API_ENDPOINTS.tasks, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });
  },

  // 删除任务
  deleteTask: async (id: string): Promise<void> => {
    return request(`${API_ENDPOINTS.tasks}/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取任务结果
  getTaskResult: async (id: string): Promise<TaskResult> => {
    return request(`${API_ENDPOINTS.tasks}/${id}/sample-results`);
  },

  // 执行任务
  executeTask: async (taskId: number): Promise<void> => {
    return request(`${API_ENDPOINTS.tasks}/${taskId}/execute`, {
      method: 'POST',
    });
  },
};

export interface CreateTaskParams {
  name: string;
  task_type: 'recall' | 'rerank';
  scene_type: string;
  sample_set_id: string;
  collection_name: string;
  top_k?: number;
  rerank_k?: number;
}

// 添加获取 collections 的接口
export const getCollections = async (): Promise<string[]> => {
  const response = await fetch(`${API_ENDPOINTS.documents}/collections`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch collections');
  }

  const data = await response.json();
  return data.data;
};

export const updateSampleSet = async (id: string, data: { name: string }): Promise<SampleSet> => {
  return request(`${API_ENDPOINTS.samples}/sample-sets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
};

// 删除文档
export const deleteDocument = async (documentId: string, sceneType: string): Promise<void> => {
  return request(`${API_ENDPOINTS.documents}/${documentId}?scene_type=${sceneType}`, {
    method: 'DELETE',
  });
};

// 创建文件夹
export const createFolder = async (name: string, sceneType: string, parentId: number = 0): Promise<void> => {
  return request(`${API_ENDPOINTS.documents}/folder?scene_type=${sceneType}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name,
      parent_id: parentId,
    }),
  });
}; 