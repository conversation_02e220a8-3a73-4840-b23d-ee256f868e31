import React, { useState } from 'react';
import { Modal, Upload, Table, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import type { NodeUnit } from '../services/api';
import { previewDocument } from '../services/api';

interface Props {
  visible: boolean;
  onCancel: () => void;
  scene: string;
  platform: string;
  uploadType: string;
}

const DocumentPreviewModal: React.FC<Props> = ({
  visible,
  onCancel,
  scene,
  platform,
  uploadType
}) => {
  const [loading, setLoading] = useState(false);
  const [previewData, setPreviewData] = useState<NodeUnit[]>([]);

  const columns = [
    {
      title: '文本内容',
      dataIndex: 'text',
      key: 'text',
    },
    {
      title: '元数据',
      dataIndex: 'metadata',
      key: 'metadata',
      render: (metadata: Record<string, any>) => (
        <pre>{JSON.stringify(metadata, null, 2)}</pre>
      ),
    }
  ];

  const handleUpload = async (file: File) => {
    setLoading(true);
    try {
      const res = await previewDocument({
        file,
        scene,
        platform,
        uploadType
      });
      
      if (res.code === 0 && res.data) {
        setPreviewData(res.data);
      } else {
        message.error(res.msg || '预览失败');
      }
    } catch (err) {
      message.error('预览失败');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="文档解析预览"
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={null}
    >
      <Upload
        accept=".pdf,.doc,.docx"
        beforeUpload={(file) => {
          handleUpload(file);
          return false;
        }}
        showUploadList={false}
      >
        <button type="button" className="ant-btn">
          <UploadOutlined /> 上传文件
        </button>
      </Upload>

      <Table
        style={{ marginTop: 16 }}
        loading={loading}
        dataSource={previewData}
        columns={columns}
        rowKey={(record) => record.text}
      />
    </Modal>
  );
};

export default DocumentPreviewModal; 