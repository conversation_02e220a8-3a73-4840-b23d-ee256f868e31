import React, { useState, useCallback } from 'react';
import { Modal, Button, Typography, Row, Col, Descriptions, Table, Space, Popconfirm, message, Select, Pagination, Skeleton } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import type { Sample, SampleSet } from '../services/api';
import { getDocumentPreviewUrl, getSamples } from '../services/api';

const { Text } = Typography;

interface SampleListModalProps {
  visible: boolean;
  sampleSet: SampleSet | null;
  samples: Sample[];
  loading: boolean;
  sceneType: string;
  onCancel: () => void;
  onAddSample: () => void;
  onEditSample: (sample: Sample) => void;
  onDeleteSample: (sampleId: string) => Promise<void>;
}

const SampleListModal: React.FC<SampleListModalProps> = ({
  visible,
  sampleSet,
  samples,
  loading,
  sceneType,
  onCancel,
  onAddSample,
  onEditSample,
  onDeleteSample,
}) => {
  // 状态声明
  const [selectedSample, setSelectedSample] = React.useState<Sample | null>(null);
  const [localSamples, setSamples] = useState<Sample[]>([]);
  const [loadingSamples, setLoadingSamples] = useState(false);
  const [totalSamples, setTotalSamples] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [currentDocIndex, setCurrentDocIndex] = useState(0);
  const [pdfUrl, setPdfUrl] = useState('');
  const [hasNext, setHasNext] = useState(true); // 是否还有更多数据
  const [loadingMore, setLoadingMore] = useState(false);
  const [scrollMode, setScrollMode] = useState(true); // true: 滚动模式, false: 分页模式
  const tableContainerRef = React.useRef<HTMLDivElement>(null);

  const fetchSamples = useCallback(async (page: number = 1, size: number = pageSize, loadMore: boolean = false) => {
    if (loadMore) {
      setLoadingMore(true);
    } else {
      setLoadingSamples(true);
    }

    try {
      const response = await getSamples(sampleSet?.id || '', page, size);
      const newItems = response.items || [];
      const total = response.total;

      if (loadMore) {
        // 累加数据
        setSamples(prev => [...prev, ...newItems]);
        setHasNext(newItems.length === size && total > ((page - 1) * size + newItems.length));
      } else {
        // 替换数据
        setSamples(newItems);
        setHasNext(newItems.length === size && total > newItems.length);
      }

      setTotalSamples(total);
      setCurrentPage(page);
      setPageSize(size);
    } catch (error) {
      console.error('Error fetching samples:', error);
      message.error('获取样本列表失败');
      setHasNext(false);
    } finally {
      setLoadingSamples(false);
      setLoadingMore(false);
    }
  }, [pageSize, sampleSet]);

  const handlePageChange = useCallback((page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    fetchSamples(page, size);
  }, [fetchSamples]);

  React.useEffect(() => {
    if (visible && sampleSet) {
      fetchSamples(1, pageSize);
      setHasNext(true); // 重置hasNext状态
    } else if (visible && !sampleSet) {
      setSamples([]);
      setTotalSamples(0);
      setCurrentPage(1);
      setHasNext(true);
    }
  }, [visible, sampleSet, pageSize, fetchSamples]);

  // 处理滚动事件监听
  React.useEffect(() => {
    const container = tableContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const threshold = 50; // 距离底部50px时触发加载

      if (scrollTop + clientHeight >= scrollHeight - threshold && hasNext && !loadingMore) {
        const nextPage = currentPage + 1;
        fetchSamples(nextPage, pageSize, true);
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasNext, loadingMore, currentPage, pageSize, fetchSamples]);

  // 回调函数用于成功添加/更新样本后的刷新
  const refreshSamples = useCallback(() => {
    fetchSamples(currentPage, pageSize);
  }, [currentPage, pageSize, fetchSamples]);

  const fetchDocumentPreviewUrl = async (documentId: string, documentName: string) => {
    try {
      const url = await getDocumentPreviewUrl(documentId, documentName, sceneType);
      setPdfUrl(url);
    } catch (error) {
      console.error('Error fetching preview URL:', error);
      message.error('获取文档预览失败');
    }
  };

  React.useEffect(() => {
    if (!visible) {
      setSelectedSample(null);
    }
  }, [visible]);

  React.useEffect(() => {
    if (selectedSample && selectedSample.documents.length > 0) {
      const doc = selectedSample.documents[currentDocIndex];
      fetchDocumentPreviewUrl(doc.document_id, doc.document_name);
    } else {
      setPdfUrl('');
    }
  }, [selectedSample, currentDocIndex]);

  const getNodeContent = (nodeContents: any, nodeId: string) => {
    // 安全访问nodeContents
    const content = nodeContents && typeof nodeContents === 'object' ? nodeContents[nodeId] : undefined;

    // 如果没有找到内容信息，返回默认值
    if (!content) {
      return {
        content: '',
        part_name: '-'
      };
    }

    // 如果内容是字符串格式
    if (typeof content === 'string') {
      return {
        content,
        part_name: '-'
      };
    }

    // 如果内容是对象格式，确保有content和part_name属性
    if (content && typeof content === 'object') {
      return {
        content: content.content || '',
        part_name: content.part_name || '-'
      };
    }

    // 其他情况返回默认值
    return {
      content: '',
      part_name: '-'
    };
  };

  const nodeColumns = [
    {
      title: '节点ID',
      dataIndex: 'node_id',
      key: 'node_id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '章节',
      dataIndex: 'part_name',
      key: 'part_name',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Text
          style={{
            display: 'block',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {text || '-'}
        </Text>
      ),
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => (
        <Text
          style={{
            display: 'block',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {text}
        </Text>
      ),
    },
  ];

  const columns = [
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
      width: '40%',
      ellipsis: true,
    },
    {
      title: '文档',
      dataIndex: 'documents',
      key: 'documents',
      width: '40%',
      render: (documents: Sample['documents'] = []) => (
        <>
          {documents.slice(0, 2).map((doc, index) => (
            <div key={index}>
              {doc.document_name} ({doc.expected_node_ids.length} 个节点)
            </div>
          ))}
          {documents.length > 2 && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              +{documents.length - 2} 个文档
            </Text>
          )}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '20%',
      render: (_: any, record: Sample) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onEditSample(record);
            }}
          />
          <Popconfirm
            title="确定要删除这个样本吗？"
            onConfirm={async (e) => {
              e?.stopPropagation();
              try {
                await onDeleteSample(record.id);
                if (selectedSample?.id === record.id) {
                  setSelectedSample(null);
                }
                // 刷新当前页数据
                fetchSamples(currentPage, pageSize);
              } catch (error) {
                console.error('Error deleting sample:', error);
              }
            }}
            onCancel={(e) => e?.stopPropagation()}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={(e) => e.stopPropagation()}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      open={visible}
      title={`样本列表 - ${sampleSet?.name || ''}`}
      onCancel={onCancel}
      width={1400}
      footer={null}
      bodyStyle={{ padding: '12px', height: '800px', overflow: 'hidden' }}
    >
      <Row style={{ height: '100%' }}>
        <Col span={8} style={{ borderRight: '1px solid #f0f0f0', height: '100%', overflow: 'hidden' }}>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <div style={{ flex: '0 0 40%', borderBottom: '1px solid #f0f0f0' }}>
              {pdfUrl ? (
                <iframe
                  src={pdfUrl}
                  style={{ width: '100%', height: '100%', border: 'none' }}
                  title="文档预览"
                />
              ) : (
                <div style={{ 
                  height: '100%', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <Text type="secondary">选择样本查看文档预览</Text>
                </div>
              )}
            </div>
            <div style={{ flex: '1 1 60%', display: 'flex', flexDirection: 'column' }}>
              <div style={{ 
                padding: '12px', 
                borderBottom: '1px solid #f0f0f0', 
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span>共 {totalSamples} 个样本</span>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={onAddSample}
                >
                  新增样本
                </Button>
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <div style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <div style={{ padding: '8px 16px', borderBottom: '1px solid #f0f0f0' }}>
                    <Space>
                      <span style={{ fontSize: '14px', color: '#666' }}>
                        展示模式:
                      </span>
                      <Select
                        size="small"
                        value={scrollMode}
                        onChange={(value) => {
                          setScrollMode(value);
                          if (!value && localSamples.length > 0) {
                            fetchSamples(1, pageSize);
                          }
                        }}
                        style={{ width: '120px' }}
                        options={[
                          { value: true, label: '无限滚动' },
                          { value: false, label: '传统分页' }
                        ]}
                      />
                    </Space>
                  </div>
                  <div style={{ flex: 1, overflow: 'hidden' }} ref={tableContainerRef}>
                {loadingSamples && localSamples.length === 0 ? (
                  <div style={{ padding: '16px' }}>
                    {[1, 2, 3].map(i => (
                      <div key={i} style={{ marginBottom: '16px' }}>
                        <Skeleton active paragraph={{ rows: 2 }} />
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    <Table
                      loading={loadingSamples && localSamples.length > 0}
                      dataSource={localSamples}
                      columns={columns}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      onRow={(record) => ({
                        onClick: () => setSelectedSample(record),
                        style: {
                          cursor: 'pointer',
                          backgroundColor: selectedSample?.id === record.id ? '#f0f0f0' : 'transparent'
                        }
                      })}
                      scroll={{ 
                        y: scrollMode ? 'calc(100vh - 600px)' : 'calc(100vh - 650px)',
                        x: 'max-content'
                      }}
                      style={{
                        height: scrollMode ? 'calc(100vh - 600px)' : 'calc(100vh - 650px)'
                      }}
                    />
                    {scrollMode && loadingMore && (
                      <div style={{ textAlign: 'center', padding: '8px', color: '#999' }}>
                        <Text type="secondary">正在加载更多...</Text>
                      </div>
                    )}
                    {scrollMode && !loadingMore && !hasNext && localSamples.length > 0 && (
                      <div style={{ textAlign: 'center', padding: '8px', color: '#999' }}>
                        <Text type="secondary">没有更多数据了</Text>
                      </div>
                    )}
                    {!scrollMode && totalSamples > 0 && (
                      <div style={{
                        padding: '16px',
                        borderTop: '1px solid #f0f0f0',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span style={{ fontSize: '14px', color: '#666' }}>
                            共 {totalSamples} 个样本
                          </span>
                          <Select
                            size="small"
                            value={pageSize}
                            onChange={(value) => handlePageChange(1, value)}
                            style={{ width: '80px' }}
                            options={[
                              { value: 10, label: '10条/页' },
                              { value: 20, label: '20条/页' },
                              { value: 50, label: '50条/页' },
                              { value: 100, label: '100条/页' }
                            ]}
                          />
                        </div>
                        <Pagination
                          current={currentPage}
                          pageSize={pageSize}
                          total={totalSamples}
                          onChange={(page, size) => handlePageChange(page, size || pageSize)}
                          showSizeChanger={false}
                          showQuickJumper
                          showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </Col>
        <Col span={16} style={{ height: '100%', overflow: 'hidden' }}>
          <div style={{ padding: '0 24px', height: '100%', overflow: 'auto' }}>
            {selectedSample ? (
              <>
                <Descriptions bordered column={1} style={{ marginTop: 24 }}>
                  <Descriptions.Item label="问题">
                    {selectedSample.question}
                  </Descriptions.Item>
                  <Descriptions.Item label="文档">
                    {selectedSample.documents.map((doc, index) => (
                      <div key={index}>
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between', 
                          alignItems: 'center',
                          marginBottom: 8 
                        }}>
                          <Typography.Title level={5} style={{ margin: 0 }}>
                            文档 {index + 1}: {doc.document_name}
                          </Typography.Title>
                          <Button
                            type={currentDocIndex === index ? 'primary' : 'default'}
                            icon={<EyeOutlined />}
                            onClick={() => setCurrentDocIndex(index)}
                          >
                            预览
                          </Button>
                        </div>
                        <Table
                          columns={nodeColumns}
                          dataSource={doc.expected_node_ids.map(nodeId => {
                            const nodeContent = getNodeContent(doc.node_contents, nodeId);
                            return {
                              node_id: nodeId,
                              content: nodeContent.content,
                              part_name: nodeContent.part_name || '-'
                            };
                          })}
                          rowKey="node_id"
                          pagination={false}
                          size="small"
                        />
                      </div>
                    ))}
                  </Descriptions.Item>
                </Descriptions>
              </>
            ) : (
              <div style={{ 
                height: '100%', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center' 
              }}>
                <Text type="secondary">请选择左侧样本查看详情</Text>
              </div>
            )}
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default SampleListModal; 
