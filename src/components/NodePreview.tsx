import React, { useState, useEffect } from 'react';
import { Table, Input, Typography, Spin, message } from 'antd';
import { fetchDocumentNodes, type Document, type NodeInfo } from '../services/api';
import { SceneType } from '../types/sample';

const { Search } = Input;
const { Text } = Typography;

interface Props {
  sceneType: SceneType;
  document: Document;
}

const NodePreview: React.FC<Props> = ({ sceneType, document }) => {
  const [nodes, setNodes] = useState<NodeInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    const fetchNodes = async () => {
      if (!sceneType || !document?.id) {
        message.error('文档信息不完整');
        return;
      }

      setLoading(true);
      try {
        const response = await fetchDocumentNodes(
          sceneType,
          document.id.toString()
        );
        setNodes(response.nodes || []);
      } catch (error) {
        console.error('Error fetching nodes:', error);
        message.error('获取节点信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchNodes();
  }, [document]);

  const filteredNodes = nodes.filter(node => 
    node.content.toLowerCase().includes(searchText.toLowerCase()) ||
    node.node_id.toLowerCase().includes(searchText.toLowerCase()) ||
    (node.part_name && node.part_name.toLowerCase().includes(searchText.toLowerCase()))
  );

  return (
    <div>
      <Search
        placeholder="搜索节点内容"
        allowClear
        onChange={e => setSearchText(e.target.value)}
        style={{ marginBottom: 16 }}
      />
      {loading ? (
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Spin />
        </div>
      ) : (
        <Table
          dataSource={filteredNodes}
          rowKey="node_id"
          columns={[
            {
              title: '节点ID',
              dataIndex: 'node_id',
              width: 200,
            },
            {
              title: '章节',
              dataIndex: 'part_name',
              width: 200,
            },
            {
              title: '内容',
              dataIndex: 'content',
              render: text => (
                <Text style={{ whiteSpace: 'pre-wrap' }}>{text}</Text>
              ),
            },
            {
              title: '是否为text_node',
              dataIndex: 'text_node',
              width: 200,
              render: text => text ? '是' : '否',
            },
          ]}
          scroll={{ y: 'calc(100vh - 300px)' }}
        />
      )}
    </div>
  );
};

export default NodePreview; 