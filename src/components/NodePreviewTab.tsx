import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Row, Col, List, Input, Select, Spin, Empty, message } from 'antd';
import { debounce } from 'lodash';
import { fetchDocuments, type Document } from '../services/api';
import { SceneType, SceneTypeLabels } from '../types/sample';
import NodePreview from './NodePreview';

const { Search } = Input;

const NodePreviewTab: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [sceneType, setSceneType] = useState<string>(SceneType.COMMON);
  const [keyword, setKeyword] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);
  const pageSize = 20;

  const containerRef = useRef<HTMLDivElement>(null);

  const [searchResults, setSearchResults] = useState<Document[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchPage, setSearchPage] = useState(1);
  const [searchHasMore, setSearchHasMore] = useState(true);
  const searchPageSize = 10;

  const fetchData = useCallback(async (isLoadMore = false) => {
    if (loading) return;
    
    setLoading(true);
    try {
      const response = await fetchDocuments({
        scene_type: sceneType,
        keyword,
        page: isLoadMore ? page + 1 : 1,
        page_size: pageSize,
      });

      const newItems = response.items || [];
      setTotal(response.total || 0);
      
      setDocuments(prev => {
        const updatedDocs = isLoadMore ? [...prev, ...newItems] : newItems;
        setHasMore((response.total || 0) > updatedDocs.length);
        return updatedDocs;
      });
      
      setPage(p => isLoadMore ? p + 1 : 1);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      message.error('获取文档列表失败');
      setDocuments([]);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [loading, sceneType, keyword, pageSize]);

  const handleSearch = useCallback(debounce(async (value: string) => {
    setSearchPage(1);
    setSearchHasMore(true);
    setKeyword(value);
    
    try {
      setSearchLoading(true);
      const response = await fetchDocuments({
        scene_type: sceneType,
        keyword: value,
        page: 1,
        page_size: searchPageSize,
      });
      setSearchResults(response.items || []);
      setSearchHasMore((response.total || 0) > searchPageSize);
    } catch (error) {
      message.error('搜索失败');
    } finally {
      setSearchLoading(false);
    }
  }, 500), [sceneType]);

  const handleSceneTypeChange = (value: string) => {
    setSceneType(value);
    setSelectedDocument(null);
    setKeyword('');
    setPage(1);
    setDocuments([]);
    setHasMore(true);
    fetchData(false);
  };

  useEffect(() => {
    setPage(1);
    setDocuments([]);
    setHasMore(true);
    fetchData(false);
  }, [sceneType, keyword]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight } = e.currentTarget;
    if (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {
      fetchData(true);
    }
  };

  const handleSearchScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop - clientHeight < 50 && 
        !searchLoading && 
        searchHasMore) {
      try {
        setSearchLoading(true);
        const nextPage = searchPage + 1;
        const response = await fetchDocuments({
          scene_type: sceneType,
          keyword: keyword || '',
          page: nextPage,
          page_size: searchPageSize,
        });

        setSearchResults(prev => [...prev, ...(response.items || [])]);
        setSearchPage(nextPage);
        setSearchHasMore((response.total || 0) > searchPageSize * nextPage);
      } catch (error) {
        message.error('加载更多失败');
      } finally {
        setSearchLoading(false);
      }
    }
  };

  return (
    <Row gutter={24} style={{ height: 'calc(100vh - 120px)' }}>
      <Col span={8}>
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          height: '100%',
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
        }}>
          <div style={{ marginBottom: 16 }}>
            <Select
              value={sceneType}
              onChange={handleSceneTypeChange}
              style={{ width: '100%', marginBottom: 12 }}
              options={Object.entries(SceneTypeLabels).map(([value, label]) => ({
                value,
                label,
              }))}
            />
            <Select
              showSearch
              placeholder="搜索并选择文档"
              filterOption={false}
              onSearch={handleSearch}
              onChange={(value, option) => {
                setSelectedDocument(option as Document);
              }}
              loading={searchLoading}
              style={{ width: '100%' }}
              options={searchResults.map(item => ({
                value: item.id,
                label: item.file_name,
                ...item
              }))}
              optionRender={(option) => (
                <div style={{ padding: '8px 12px' }}>
                  {option.label}
                </div>
              )}
              dropdownStyle={{
                maxHeight: 400,
                overflow: 'auto',
                borderRadius: '6px',
                boxShadow: '0 3px 6px rgba(0,0,0,0.1)'
              }}
              onPopupScroll={handleSearchScroll}
              onFocus={() => {
                if (searchResults.length === 0) {
                  handleSearch(keyword);
                }
              }}
            />
          </div>
        </div>
      </Col>
      <Col span={16}>
        <div style={{ 
          height: '100%',
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
        }}>
          {selectedDocument ? (
            <NodePreview sceneType={sceneType as SceneType} document={selectedDocument} />
          ) : (
            <Empty 
              description="请选择文档以预览节点" 
              style={{ marginTop: '20%' }}
            />
          )}
        </div>
      </Col>
    </Row>
  );
};

export default NodePreviewTab; 