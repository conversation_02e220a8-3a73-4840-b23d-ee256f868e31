import React, { useState } from 'react';
import { Tree, Space, Button, Dropdown, message } from 'antd';
import type { TreeDataNode, TreeProps } from 'antd';
import {
    FolderOutlined,
    FolderOpenOutlined,
    FileOutlined,
    MoreOutlined,
    EyeOutlined,
    DeleteOutlined,
    EditOutlined,
    PlusOutlined
} from '@ant-design/icons';
import type { Document } from '../services/api';

// 扩展TreeDataNode以包含文档数据
interface ExtendedTreeDataNode extends TreeDataNode {
    document?: Document;
}

interface DocumentTreeProps {
    documents: Document[];
    onPreview: (document: Document) => void;
    onDelete?: (document: Document) => void;
    onCreateFolder?: (parentId?: string) => void;
}

const DocumentTree: React.FC<DocumentTreeProps> = ({
    documents,
    onPreview,
    onDelete,
    onCreateFolder
}) => {
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['0']);
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

    // 创建文档ID到文档对象的映射
    const documentMap = new Map<string, Document>();
    documents.forEach(doc => {
        documentMap.set(doc.id, doc);
    });

    // 将扁平的文档列表转换为树形结构
    const buildTreeData = (docs: Document[]): ExtendedTreeDataNode[] => {
        const nodeMap = new Map<string, ExtendedTreeDataNode>();
        const rootNodes: ExtendedTreeDataNode[] = [];

        // 首先创建所有节点
        docs.forEach(doc => {
            const node: ExtendedTreeDataNode = {
                key: doc.id,
                title: renderTreeNodeTitle(doc),
                icon: doc.is_dir ? <FolderOutlined /> : <FileOutlined />,
                isLeaf: !doc.is_dir,
                children: [],
                document: doc, // 存储原始数据
            };
            nodeMap.set(doc.id, node);
        });

        // 然后建立父子关系
        docs.forEach(doc => {
            const node = nodeMap.get(doc.id);
            if (!node) return;

            if (!doc.parent_id || doc.parent_id === 0) {
                // 根节点
                rootNodes.push(node);
            } else {
                // 子节点
                const parentNode = nodeMap.get(doc.parent_id.toString());
                if (parentNode && parentNode.children) {
                    parentNode.children.push(node);
                }
            }
        });

        return rootNodes;
    };

    const renderTreeNodeTitle = (doc: Document) => {
        const getMenuItems = (document: Document) => [
            {
                key: 'preview',
                label: '预览',
                icon: <EyeOutlined />,
                disabled: document.is_dir,
                onClick: () => onPreview(document),
            },
            ...(onCreateFolder ? [{
                key: 'addFolder',
                label: '新建子文件夹',
                icon: <PlusOutlined />,
                disabled: !document.is_dir,
                onClick: () => onCreateFolder(document.id),
            }] : []),
            ...(onCreateFolder || onDelete ? [{
                type: 'divider' as const,
            }] : []),
            {
                key: 'rename',
                label: '重命名',
                icon: <EditOutlined />,
                onClick: () => message.info('重命名功能开发中'),
            },
            ...(onDelete ? [{
                key: 'delete',
                label: '删除',
                icon: <DeleteOutlined />,
                danger: true,
                onClick: () => onDelete(document),
            }] : []),
        ].filter(Boolean);

        return (
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                    padding: '4px 0'
                }}
            >
                <Space size="small">
                    <span style={{
                        fontWeight: doc.is_dir ? 500 : 400,
                        color: doc.is_dir ? '#262626' : '#595959'
                    }}>
                        {doc.file_name}
                    </span>

                    {/* 状态标识 */}
                    {!doc.is_dir && doc.upload_status && (
                        <span style={{
                            fontSize: '10px',
                            padding: '1px 4px',
                            borderRadius: '2px',
                            backgroundColor: doc.upload_status === 'INDEX_BUILD_SUCCESS' ? '#f6ffed' : '#fff7e6',
                            color: doc.upload_status === 'INDEX_BUILD_SUCCESS' ? '#52c41a' : '#faad14'
                        }}>
                            {doc.upload_status === 'INDEX_BUILD_SUCCESS' ? '✓' : '⏳'}
                        </span>
                    )}
                </Space>

                <Dropdown
                    menu={{ items: getMenuItems(doc) }}
                    trigger={['click']}
                    placement="bottomRight"
                >
                    <Button
                        type="text"
                        size="small"
                        icon={<MoreOutlined />}
                        style={{ opacity: 0.6 }}
                        onClick={(e) => e.stopPropagation()}
                    />
                </Dropdown>
            </div>
        );
    };

    const onExpand: TreeProps['onExpand'] = (expandedKeysValue) => {
        setExpandedKeys(expandedKeysValue);
    };

    const onSelect: TreeProps['onSelect'] = (selectedKeysValue, info) => {
        setSelectedKeys(selectedKeysValue);

        // 如果选中的是文件，自动预览
        const selectedDoc = documentMap.get(info.node.key as string);
        if (selectedDoc && !selectedDoc.is_dir) {
            onPreview(selectedDoc);
        }
    };

    const treeData = buildTreeData(documents);

    return (
        <div style={{
            height: '600px',
            overflow: 'auto',
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            padding: '8px'
        }}>
            <Tree
                showIcon
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onExpand={onExpand}
                onSelect={onSelect}
                treeData={treeData}
                switcherIcon={({ expanded }) =>
                    expanded ? <FolderOpenOutlined /> : <FolderOutlined />
                }
                style={{ fontSize: '14px' }}
            />

            <style>{`
        .ant-tree .ant-tree-node-content-wrapper {
          width: calc(100% - 24px);
        }
        
        .ant-tree .ant-tree-title {
          width: 100%;
        }
        
        .ant-tree-node-selected .ant-tree-title {
          background-color: #e6f7ff !important;
        }
        
        .ant-tree-node-selected .ant-tree-node-content-wrapper {
          background-color: #e6f7ff !important;
        }
      `}</style>
        </div>
    );
};

export default DocumentTree;