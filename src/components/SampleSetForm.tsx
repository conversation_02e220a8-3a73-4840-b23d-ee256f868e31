import React from 'react';
import { Form, Input, Modal, Select } from 'antd';
import { SceneType, SceneTypeLabels } from '../types/sample';

interface SampleSetFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: { name: string; scene_type: string }) => void;
}

const SampleSetForm: React.FC<SampleSetFormProps> = ({
  visible,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  const handleOk = () => {
    form.validateFields().then(values => {
      onSubmit(values);
      form.resetFields();
    });
  };

  return (
    <Modal
      open={visible}
      title="新增样本集"
      onCancel={onCancel}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="样本集名称"
          rules={[{ required: true, message: '请输入样本集名称' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="scene_type"
          label="场景类型"
          rules={[{ required: true, message: '请选择场景类型' }]}
        >
          <Select>
            {Object.entries(SceneTypeLabels).map(([value, label]) => (
              <Select.Option key={value} value={value}>
                {label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SampleSetForm; 