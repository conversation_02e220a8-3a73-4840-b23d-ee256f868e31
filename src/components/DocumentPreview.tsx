import React, { useState } from 'react';
import { 
  Upload, 
  Button, 
  Select, 
  Form, 
  message, 
  Table,
  InputNumber,
  Collapse,
  Card,
  Space,
  Divider
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import { previewDocument, PreviewResponse, NodeUnit } from '../services/api';

const { Option } = Select;
const { Panel } = Collapse;

const DocumentPreview: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewData, setPreviewData] = useState<NodeUnit[]>([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handlePreview = async () => {
    if (fileList.length === 0) {
      message.error('请先选择文件');
      return;
    }

    const values = await form.validateFields();
    setLoading(true);

    try {
      const response = await previewDocument({
        file: fileList[0].originFileObj as File,
        scene: values.scene,
        platform: values.platform,
        uploadType: values.uploadType,
        general_parser_chunk_size: values.general_parser_chunk_size,
        general_parser_chunk_overlap: values.general_parser_chunk_overlap,
        table_chunk_size: values.table_chunk_size,
        table_chunk_overlap: values.table_chunk_overlap,
      });

      if (response.code === 0) {
        setPreviewData(response.data || []);
        message.success('解析成功');
      } else {
        message.error(response.msg || '预览失败');
      }
    } catch (error) {
      message.error('预览请求失败');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      fixed: 'left' as const,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '内容',
      dataIndex: 'text',
      key: 'text',
      width: '45%',
      render: (text: string) => (
        <div style={{ 
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '200px',
          overflow: 'auto',
          padding: '8px',
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '4px'
        }}>
          {text}
        </div>
      ),
    },
    {
      title: '元数据',
      dataIndex: 'metadata',
      key: 'metadata',
      width: '45%',
      render: (metadata: any) => {
        const formattedMetadata = {
          文档名称: metadata.file_name,
          页码: metadata.page_number,
          段落类型: metadata.paragraph_type,
          表格标题: metadata.table_title,
          表格位置: metadata.table_position,
          标题名:   metadata.part_name,
          ...metadata
        };

        return (
          <div style={{ 
            maxHeight: '200px', 
            overflow: 'auto',
            margin: 0,
            padding: '8px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            {Object.entries(formattedMetadata)
              .filter(([key, value]) => value)
              .map(([key, value]) => (
                <div key={key} style={{ marginBottom: '4px' }}>
                  <span style={{ color: '#666', marginRight: '8px' }}>{key}:</span>
                  <span>{String(value)}</span>
                </div>
              ))}
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            scene: 'common',
            platform: 'common',
            uploadType: 'file',
            general_parser_chunk_size: 500,
            general_parser_chunk_overlap: 10,
            table_chunk_size: 4096,
            table_chunk_overlap: 100,
          }}
        >
          <div style={{ display: 'flex', gap: '24px' }}>
            <Form.Item
              name="scene"
              label="场景"
              rules={[{ required: true }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Select.Option value="common">通用</Select.Option>
                <Select.Option value="bj_telecom">北京电信</Select.Option>
                <Select.Option value="cmcc">移动</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="platform"
              label="平台"
              rules={[{ required: true }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Select.Option value="common">通用</Select.Option>
                <Select.Option value="bj_telecom">北京电信</Select.Option>
                <Select.Option value="cmcc">移动</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="uploadType"
              label="上传类型"
              rules={[{ required: true }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Select.Option value="file">文件</Select.Option>
                <Select.Option value="url">URL</Select.Option>
              </Select>
            </Form.Item>
          </div>

          <Collapse 
            ghost 
            style={{ 
              background: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '4px',
              marginBottom: '24px'
            }}
          >
            <Panel header="高级配置" key="1">
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <Form.Item
                  name="general_parser_chunk_size"
                  label="通用解析块大小"
                  rules={[{ required: true }]}
                >
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="general_parser_chunk_overlap"
                  label="通用解析块重叠"
                  rules={[{ required: true }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="table_chunk_size"
                  label="表格解析块大小"
                  rules={[{ required: true }]}
                >
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="table_chunk_overlap"
                  label="表格解析块重叠"
                  rules={[{ required: true }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </div>
            </Panel>
          </Collapse>

          <Space size="large">
            <Upload
              accept=".pdf,.doc,.docx"
              beforeUpload={() => false}
              onChange={({ fileList }) => setFileList(fileList)}
              fileList={fileList}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>

            <Button 
              type="primary" 
              onClick={handlePreview}
              loading={loading}
            >
              开始解析
            </Button>
          </Space>
        </Form>

        <Divider />

        {previewData.length > 0 && (
          <>
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <span>解析结果：</span>
                <span style={{ color: '#1890ff' }}>
                  共 {previewData.length} 个片段
                </span>
              </Space>
            </div>

            <Table
              dataSource={previewData}
              columns={columns}
              rowKey={(record) => record.text}
              pagination={{ 
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条`,
                pageSizeOptions: ['10', '20', '50', '100'],
              }}
              scroll={{ 
                x: 1200,
                y: 'calc(100vh - 500px)' 
              }}
              loading={loading}
              bordered
              size="small"
              expandable={{
                expandedRowRender: (record) => (
                  <div style={{ padding: '16px' }}>
                    <h4>完整内容：</h4>
                    <div style={{ 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      padding: '16px',
                      backgroundColor: '#fff',
                      border: '1px solid #f0f0f0',
                      borderRadius: '4px',
                      marginTop: '8px'
                    }}>
                      {record.text}
                    </div>
                    <h4 style={{ marginTop: '16px' }}>完整元数据：</h4>
                    <pre style={{ 
                      padding: '16px',
                      backgroundColor: '#f5f5f5',
                      border: '1px solid #f0f0f0',
                      borderRadius: '4px',
                      marginTop: '8px',
                      overflow: 'auto'
                    }}>
                      {JSON.stringify(record.metadata, null, 2)}
                    </pre>
                  </div>
                ),
              }}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default DocumentPreview; 