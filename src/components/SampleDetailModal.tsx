import React from 'react';
import { Modal, Descriptions, Table, Typography } from 'antd';
import type { Sample, SampleNode } from '../services/api';

const { Text } = Typography;

interface SampleDetailModalProps {
  visible: boolean;
  sample: Sample | null;
  onCancel: () => void;
}

const SampleDetailModal: React.FC<SampleDetailModalProps> = ({
  visible,
  sample,
  onCancel,
}) => {
  const columns = [
    {
      title: '节点ID',
      dataIndex: 'node_id',
      key: 'node_id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => (
        <Text
          style={{
            display: 'block',
            maxHeight: '200px',
            overflowY: 'auto',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {text}
        </Text>
      ),
    },
  ];

  return (
    <Modal
      open={visible}
      title="样本详情"
      onCancel={onCancel}
      width={1000}
      footer={null}
    >
      {sample && (
        <>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="问题">
              {sample.question}
            </Descriptions.Item>
            <Descriptions.Item label="文档">
              {sample.documents?.map(doc => doc.document_name).join(', ') || '无'}
            </Descriptions.Item>
          </Descriptions>
          
          <Typography.Title level={5} style={{ marginTop: 24, marginBottom: 16 }}>
            预期召回节点
          </Typography.Title>
          <Table
            columns={columns}
            dataSource={sample.documents?.flatMap(doc => 
              doc.expected_node_ids.map(nodeId => ({
                node_id: nodeId,
                content: doc.node_contents?.[nodeId] || nodeId
              }))
            ) || []}
            rowKey="node_id"
            pagination={false}
            scroll={{ y: 400 }}
          />
        </>
      )}
    </Modal>
  );
};

export default SampleDetailModal; 