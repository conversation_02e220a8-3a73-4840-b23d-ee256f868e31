import React from 'react';
import { Select, Spin } from 'antd';
import type { SelectProps } from 'antd';
import debounce from 'lodash/debounce';
import { API_ENDPOINTS } from '../config';

export interface DocumentOption {
  label: string;
  value: string;
}

interface DocumentSelectProps {
  value?: string;
  onChange?: (value: string, option?: DocumentOption | DocumentOption[]) => void;
  sceneType: string;
  defaultLabel?: string;
}

const DocumentSelect: React.FC<DocumentSelectProps> = ({
  value,
  onChange,
  sceneType,
  defaultLabel,
}) => {
  const [fetching, setFetching] = React.useState(false);
  const [options, setOptions] = React.useState<DocumentOption[]>([]);

  const fetchDocuments = async (keyword: string) => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.documents}/?${new URLSearchParams({
          scene_type: sceneType,
          keyword: keyword || '',
          page: '1',
          page_size: '10',
        })}`
      );
      const data = await response.json();
      return data.items.map((doc: any) => ({
        label: doc.file_name,
        value: doc.id,
      }));
    } catch (error) {
      console.error('Error fetching documents:', error);
      return [];
    }
  };

  const debouncedFetch = React.useMemo(
    () => debounce(async (keyword: string) => {
      setFetching(true);
      const newOptions = await fetchDocuments(keyword);
      setOptions(newOptions);
      setFetching(false);
    }, 500),
    [sceneType]
  );

  React.useEffect(() => {
    debouncedFetch('');
  }, [sceneType]);

  React.useEffect(() => {
    if (value && defaultLabel) {
      setOptions(prev => {
        if (!prev.some(opt => opt.value === value)) {
          return [...prev, { value, label: defaultLabel }];
        }
        return prev;
      });
    }
  }, [value, defaultLabel]);

  return (
    <Select
      showSearch
      value={value}
      placeholder="请输入关键词搜索文档"
      defaultActiveFirstOption={false}
      filterOption={false}
      onSearch={debouncedFetch}
      onChange={onChange}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      options={options}
      style={{ width: '100%' }}
      loading={fetching}
    />
  );
};

export default DocumentSelect; 