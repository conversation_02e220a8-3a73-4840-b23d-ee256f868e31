import React, { useState } from 'react';
import { Form, Input, Modal, Select, Table, Spin, Row, Col, Typography, message, Button } from 'antd';
import type { FormValues, EntityType } from '../types/operations';
import { SceneType, SceneTypeLabels } from '../types/sample';
import DocumentSelect from './DocumentSelect';
import { fetchDocumentNodes, type NodeInfo, getDocumentPreviewUrl } from '../services/api';

const { Text } = Typography;

interface EntityFormProps {
  type: EntityType;
  visible: boolean;
  initialValues?: FormValues;
  onCancel: () => void;
  onSubmit: (values: FormValues) => void;
  collections?: string[];
}

interface DocumentData {
  documentId: string;
  documentName: string;
  expectedNodeIds: string[];
}

const EntityForm: React.FC<EntityFormProps> = ({
  type,
  collections,
  visible,
  initialValues,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [nodes, setNodes] = React.useState<NodeInfo[][]>([]);
  const [loadingNodes, setLoadingNodes] = React.useState(false);
  const [selectedDocName, setSelectedDocName] = React.useState<string>('');
  const [pdfUrl, setPdfUrl] = React.useState<string>('');
  const [documents, setDocuments] = React.useState<DocumentData[]>([]);
  const [currentDocIndex, setCurrentDocIndex] = React.useState(0);

  React.useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue({
          question: initialValues.question,
          documents: initialValues.documents?.map(doc => ({
            documentId: doc.documentId,
            documentName: doc.documentName,
            expectedNodeIds: doc.expectedNodeIds,
          })) || []
        });
        
        // 如果是编辑模式，需要加载每个文档的节点
        if (type === 'sample' && initialValues.documents) {
          initialValues.documents.forEach((doc, index) => {
            handleDocumentChange(
              doc.documentId,
              { label: doc.documentName },
              index
            );
          });
          setDocuments(initialValues.documents);
        }
      }
    } else {
      form.resetFields();
      setNodes([]);
      setLoadingNodes(false);
    }
  }, [visible, initialValues, form, type]);

  const fetchDocumentPreviewUrl = async (documentId: string, documentName: string) => {
    try {
      const url = await getDocumentPreviewUrl(documentId, documentName, initialValues?.scene_type || SceneType.COMMON);
      setPdfUrl(url);
    } catch (error) {
      console.error('Error fetching preview URL:', error);
      message.error('获取文档预览失败');
    }
  };

  const handleDocumentChange = async (documentId: string, option: { label: string }, index: number) => {
    const currentNodeIds = form.getFieldValue(['documents', index, 'expectedNodeIds']) || [];
    const isEditing = initialValues?.document_id === documentId;
    
    if (!isEditing) {
      form.setFieldValue(['documents', index, 'expectedNodeIds'], []);
    }
    
    form.setFieldsValue({
      documents: {
        [index]: {
          documentId: documentId,
          documentName: option.label,
          expectedNodeIds: currentNodeIds
        }
      }
    });
    
    setSelectedDocName(option.label);
    
    if (!documentId) {
      const newNodes = [...nodes];
      newNodes[index] = [];
      setNodes(newNodes);
      return;
    }

    setLoadingNodes(true);
    try {
      const sceneType = initialValues?.scene_type || SceneType.COMMON;
      const response = await fetchDocumentNodes(sceneType, documentId);
      const newNodes = [...nodes];
      newNodes[index] = response.nodes;
      setNodes(newNodes);

      if (isEditing && currentNodeIds.length > 0) {
        const validNodeIds = currentNodeIds.filter((nodeId: string) => 
          response.nodes.some(node => node.node_id === nodeId)
        );
        
        if (validNodeIds.length !== currentNodeIds.length) {
          message.warning('部分预期节点在当前文档中不存在，已自动移除');
          form.setFieldValue(['documents', index, 'expectedNodeIds'], validNodeIds);
        }
      }
    } catch (error) {
      console.error('Error fetching nodes:', error);
      message.error('获取文档节点失败');
    } finally {
      setLoadingNodes(false);
    }

    if (documentId) {
      fetchDocumentPreviewUrl(
        documentId,
        option.label
      );
    } else {
      setPdfUrl('');
    }
  };

  React.useEffect(() => {
    const currentDoc = form.getFieldValue(['documents', currentDocIndex]);
    if (currentDoc?.documentId) {
      fetchDocumentPreviewUrl(currentDoc.documentId, currentDoc.documentName);
    } else {
      setPdfUrl('');
    }
  }, [currentDocIndex]);

  const nodeColumns = [
    {
      title: '节点ID',
      dataIndex: 'node_id',
      key: 'node_id',
      width: 120,
      ellipsis: true,
    },
    {
      title: '是否TEXT-NODE',
      dataIndex: 'text_node',
      key: 'text_node',
      width: 120,
      render: (text_node: boolean) => (
        <Text style={{ color: text_node ? '#52c41a' : '#ff4d4f' }}>
          {text_node ? '是' : '否'}
        </Text>
      ),
    },
    {
      title: '章节',
      dataIndex: 'part_name',
      key: 'part_name',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Text
          style={{
            display: 'block',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {text || '-'}
        </Text>
      ),
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => (
        <Text
          style={{
            display: 'block',
            maxHeight: '200px',
            overflowY: 'auto',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {text}
        </Text>
      ),
    },
  ];

  const handleOk = () => {
    form.validateFields().then(async values => {
      try {
        if (type === 'sample') {
          const documents = values.documents.map((doc: any) => ({
            documentId: doc.documentId,
            documentName: doc.documentName,
            expectedNodeIds: doc.expectedNodeIds
          }));

          await onSubmit({
            ...values,
            documents,
            sceneType: initialValues?.scene_type || SceneType.COMMON,
            sampleSetId: values.sample_set_id
          });

          form.resetFields();
          setNodes([]);
          setPdfUrl('');
          setDocuments([]);
          setCurrentDocIndex(0);
          onCancel();
          message.success('样本创建成功');
        } else {
          await onSubmit(values);
          form.resetFields();
          onCancel();
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        message.error('提交失败，请重试');
      }
    });
  };

  const handleAddDocument = () => {
    if (documents.length >= 3) {
      message.warning('最多只能添加3个文档');
      return;
    }
    setDocuments([...documents, { documentId: '', documentName: '', expectedNodeIds: [] }]);
    setNodes([...nodes, []]);
  };

  const handleRemoveDocument = (index: number) => {
    const newDocs = [...documents];
    newDocs.splice(index, 1);
    setDocuments(newDocs);
    const newNodes = [...nodes];
    newNodes.splice(index, 1);
    setNodes(newNodes);
    if (currentDocIndex >= newDocs.length) {
      setCurrentDocIndex(Math.max(0, newDocs.length - 1));
    }
  };

  const renderSampleForm = () => (
    <Row>
      <Col span={6} style={{ paddingRight: 16 }}>
        <Form.Item
          name="question"
          label="问题"
          rules={[{ required: true, message: '请输入问题' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>
        
        <div style={{ marginBottom: 16 }}>
          <Button onClick={handleAddDocument} disabled={documents.length >= 3}>
            添加文档
          </Button>
          
          {documents.map((doc, index) => (
            <div key={index} style={{ marginTop: 8, padding: 8, border: '1px solid #d9d9d9' }}>
              <Row justify="space-between" align="middle">
                <Col>文档 {index + 1}</Col>
                <Col>
                  <Button 
                    type={currentDocIndex === index ? 'primary' : 'default'}
                    onClick={() => setCurrentDocIndex(index)}
                    style={{ marginRight: 8 }}
                  >
                    预览
                  </Button>
                  <Button danger onClick={() => handleRemoveDocument(index)}>
                    删除
                  </Button>
                </Col>
              </Row>
              
              <Form.Item
                name={['documents', index, 'documentId']}
                rules={[{ required: true, message: '请选择文档' }]}
              >
                <DocumentSelect 
                  sceneType={initialValues?.scene_type || SceneType.COMMON}
                  onChange={(docId, option) => {
                    if (option && !Array.isArray(option)) {
                      handleDocumentChange(docId, option as { label: string }, index);
                    }
                  }}
                />
              </Form.Item>
              
              <Form.Item
                name={['documents', index, 'documentName']}
                hidden
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                name={['documents', index, 'expectedNodeIds']}
                rules={[{ required: true, message: '请选择预期召回节点' }]}
              >
                <Select
                  mode="tags"
                  placeholder="请选择节点"
                  options={nodes[index]?.map(node => ({
                    label: `${node.node_id} - ${node.content.slice(0, 30)}...`,
                    value: node.node_id,
                  }))}
                  optionFilterProp="label"
                  showSearch
                />
              </Form.Item>
            </div>
          ))}
        </div>
      </Col>

      <Col span={10} style={{ height: '100%', overflow: 'auto', padding: '0 16px' }}>
        <Typography.Title level={5} style={{ marginTop: 0 }}>当前文档节点列表</Typography.Title>
        {loadingNodes ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin />
          </div>
        ) : nodes[currentDocIndex]?.length > 0 ? (
          <Table
            columns={nodeColumns}
            dataSource={nodes[currentDocIndex]}
            rowKey="node_id"
            size="small"
            scroll={{ y: 'calc(100vh - 250px)' }}
            pagination={false}
            onRow={(record) => ({
              onClick: () => {
                const currentIds = form.getFieldValue(['documents', currentDocIndex, 'expectedNodeIds']) || [];
                if (!currentIds.includes(record.node_id)) {
                  form.setFieldValue(['documents', currentDocIndex, 'expectedNodeIds'], 
                    [...currentIds, record.node_id]
                  );
                }
              },
              style: { cursor: 'pointer' }
            })}
          />
        ) : (
          <Text type="secondary">请选择文档查看节点列表</Text>
        )}
      </Col>

      <Col span={8} style={{ height: '100%', overflow: 'hidden' }}>
        {pdfUrl ? (
          <iframe
            src={pdfUrl}
            style={{ width: '100%', height: 'calc(100vh - 200px)', border: 'none' }}
            title="文档预览"
          />
        ) : (
          <div style={{ 
            height: 'calc(100vh - 200px)', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            border: '1px dashed #d9d9d9',
            borderRadius: '2px'
          }}>
            <Text type="secondary">请选择文档查看预览</Text>
          </div>
        )}
      </Col>
    </Row>
  );

  const renderFormItems = () => {
    switch (type) {
      case 'scene':
        return (
          <>
            <Form.Item
              name="name"
              label="场景名称"
              rules={[{ required: true, message: '请输入场景名称' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="sceneType"
              label="场景类型"
              rules={[{ required: true, message: '请选择场景类型' }]}
            >
              <Select
                options={Object.entries(SceneTypeLabels).map(([value, label]) => ({
                  value,
                  label
                }))}
              />
            </Form.Item>
          </>
        );
      case 'sampleSet':
        return (
          <Form.Item
            name="name"
            label="样本集名称"
            rules={[{ required: true, message: '请输入样本集名称' }]}
          >
            <Input />
          </Form.Item>
        );
      case 'sample':
        return renderSampleForm();
    }
  };

  return (
    <Modal
      open={visible}
      title={`${initialValues ? '编辑' : '新增'}${
        type === 'scene' ? '场景' : type === 'sampleSet' ? '样本集' : '样本'
      }`}
      onCancel={() => {
        onCancel();
        form.resetFields();
        setNodes([]);
        setPdfUrl('');
        setDocuments([]);
        setCurrentDocIndex(0);
      }}
      onOk={handleOk}
      width={1600}
      style={{ top: 20 }}
    >
      <Form form={form} layout="vertical">
        {renderFormItems()}
        <Form.Item name="document_name" hidden>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EntityForm; 