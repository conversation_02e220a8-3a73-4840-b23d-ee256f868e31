import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Space, message, Select, Row, Col, List, Form, Input, Modal, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import { 
  createSampleSet, 
  getSampleSets, 
  getSamples, 
  createSample,
  updateSample,
  deleteSample,
  updateSampleSet,
  type SampleSet, 
  type Sample,
  getCollections
} from '../services/api';
import SampleSetForm from '../components/SampleSetForm';
import EntityForm from '../components/EntityForm';
import SampleListModal from '../components/SampleListModal';
import { SceneType, SceneTypeLabels } from '../types/sample';
import { FormInstance } from 'antd';

const { Text } = Typography;

const CreateSampleSetForm: React.FC<{
  form: FormInstance;
  onSuccess: () => void;
  collections: string[];
}> = React.memo(({ form, onSuccess, collections }) => {
  const handleSubmit = useCallback(async (values: any) => {
    try {
      await createSampleSet(values);
      message.success('创建样本集成功');
      form.resetFields();
      onSuccess();
    } catch (error) {
      message.error('创建样本集失败');
    }
  }, [form, onSuccess]);

  return (
    <Form form={form} onFinish={handleSubmit}>
      <Form.Item
        name="name"
        label="样本集名称"
        rules={[{ required: true, message: '请输入样本集名称' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="scene_type"
        label="场景类型"
        rules={[{ required: true, message: '请选择场景类型' }]}
      >
        <Select>
          <Select.Option value="common">通用</Select.Option>
          <Select.Option value="bj_telecom">北京电信</Select.Option>
          <Select.Option value="cmcc">移动</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="collection_name"
        label="向量库集合"
        rules={[{ required: true, message: '请选择向量库集合' }]}
      >
        <Select
          showSearch
          placeholder="请选择向量库集合"
          optionFilterProp="children"
        >
          {collections.map(collection => (
            <Select.Option key={collection} value={collection}>
              {collection}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    </Form>
  );
});

CreateSampleSetForm.displayName = 'CreateSampleSetForm';

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: string;
  record: SampleSet;
  index: number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  record,
  index,
  children,
  ...restProps
}) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: true,
              message: `请输入${title}!`,
            },
          ]}
        >
          <Input />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const SampleManagement: React.FC = () => {
  const [sceneType, setSceneType] = useState<string>(SceneType.COMMON);
  const [sampleSets, setSampleSets] = useState<SampleSet[]>([]);
  const [samples, setSamples] = useState<Sample[]>([]);
  const [selectedSampleSet, setSelectedSampleSet] = useState<SampleSet | null>(null);
  const [loading, setLoading] = useState(false);
  const [sampleSetModalVisible, setSampleSetModalVisible] = useState(false);
  const [sampleListModalVisible, setSampleListModalVisible] = useState(false);
  const [sampleModalVisible, setSampleModalVisible] = useState(false);
  const [editingSample, setEditingSample] = useState<Sample | null>(null);
  const [collections, setCollections] = useState<string[]>([]);
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState<string>('');

  useEffect(() => {
    const fetchCollections = async () => {
      try {
        const data = await getCollections();
        setCollections(data);
      } catch (error) {
        message.error('获取向量库集合列表失败');
      }
    };
    
    fetchCollections();
  }, []);

  const fetchSampleSets = async () => {
    setLoading(true);
    try {
      const response = await getSampleSets(sceneType);
      setSampleSets(response.items || []);
    } catch (error) {
      console.error('Error fetching sample sets:', error);
      message.error('获取样本集列表失败');
      setSampleSets([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSampleSetClick = async (sampleSet: SampleSet) => {
    setSelectedSampleSet(sampleSet);
    setSampleListModalVisible(true);
    try {
      const response = await getSamples(sampleSet.id);
      setSamples(response.items || []);
    } catch (error) {
      console.error('Error fetching samples:', error);
      message.error('获取样本列表失败');
      setSamples([]);
    }
  };

  React.useEffect(() => {
    fetchSampleSets();
  }, [sceneType]);

  const handleCreateSampleSet = async (values: any) => {
    try {
      await createSampleSet({
        name: values.name,
        scene_type: sceneType,
        collection_name: values.collection_name
      });
      message.success('创建样本集成功');
      setSampleSetModalVisible(false);
      form.resetFields();
      fetchSampleSets();
    } catch (error) {
      message.error('创建样本集失败');
    }
  };

  const handleDeleteSample = async (sampleId: string) => {
    try {
      await deleteSample(sampleId);
      message.success('删除样本成功');
      if (selectedSampleSet) {
        fetchSampleSets();
      }
    } catch (error) {
      console.error('Error deleting sample:', error);
      message.error('删除样本失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  const isEditing = (record: SampleSet) => record.id === editingKey;

  const edit = (record: SampleSet) => {
    form.setFieldsValue({ name: record.name });
    setEditingKey(record.id);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async (id: string) => {
    try {
      const values = await form.validateFields();
      
      await updateSampleSet(id, values);
      message.success('修改成功');
      setEditingKey('');
      fetchSampleSets();
    } catch (errInfo) {
      console.error('Save failed:', errInfo);
      message.error('修改失败');
    }
  };

  const columns: ColumnsType<SampleSet> = [
    {
      title: '样本集名称',
      dataIndex: 'name',
      key: 'name',
      editable: true,
      render: (text: string, record: SampleSet) => {
        const editable = isEditing(record);
        return editable ? (
          <Text>{text}</Text>
        ) : (
          <Text>{text}</Text>
        );
      },
    },
    {
      title: '场景类型',
      dataIndex: 'scene_type',
      key: 'scene_type',
      render: (text: string) => ({
        'common': '通用',
        'bj_telecom': '北京电信',
        'cmcc': '移动'
      }[text] || text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SampleSet) => {
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Button
              type="link"
              onClick={() => save(record.id)}
              style={{ marginRight: 8 }}
            >
              保存
            </Button>
            <Button type="link" onClick={cancel}>
              取消
            </Button>
          </span>
        ) : (
          <span>
            <Button
              type="link"
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              修改名称
            </Button>
            <Button type="link" onClick={() => handleSampleSetClick(record)}>
              查看样本
            </Button>
          </span>
        );
      },
    },
  ].map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: SampleSet) => ({
        record,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <div style={{ padding: 24 }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Select
              value={sceneType}
              onChange={setSceneType}
              style={{ width: 200 }}
              options={Object.entries(SceneTypeLabels).map(([value, label]) => ({
                value,
                label,
              }))}
            />
          </Col>
          <Col>
            <Button type="primary" onClick={() => setSampleSetModalVisible(true)}>
              新增样本集
            </Button>
          </Col>
        </Row>

        <Form form={form} component={false}>
          <Table
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            columns={columns}
            dataSource={sampleSets}
            rowKey="id"
            loading={loading}
          />
        </Form>
      </Space>

      <EntityForm
        type="sampleSet"
        visible={sampleSetModalVisible}
        onCancel={() => setSampleSetModalVisible(false)}
        onSubmit={handleCreateSampleSet}
        collections={collections}
        initialValues={{ scene_type: sceneType }}
      />

      <SampleListModal
        visible={sampleListModalVisible}
        sampleSet={selectedSampleSet}
        samples={samples}
        loading={loading}
        sceneType={sceneType}
        onCancel={() => {
          setSampleListModalVisible(false);
          setSelectedSampleSet(null);
          setSamples([]);
        }}
        onAddSample={() => {
          setSampleModalVisible(true);
          setSampleListModalVisible(false);
        }}
        onEditSample={(sample) => {
          setEditingSample(sample);
          setSampleModalVisible(true);
          setSampleListModalVisible(false);
        }}
        onDeleteSample={handleDeleteSample}
      />

      <EntityForm
        type="sample"
        visible={sampleModalVisible}
        initialValues={{
          ...editingSample,
          question: editingSample?.question,
          documents: editingSample?.documents.map(doc => ({
            documentId: doc.document_id,
            documentName: doc.document_name,
            expectedNodeIds: doc.expected_node_ids,
          })) || [],
          sample_set_id: selectedSampleSet?.id,
          scene_type: sceneType,
        }}
        onCancel={() => {
          setSampleModalVisible(false);
          setEditingSample(null);
          setSampleListModalVisible(true);
        }}
        onSubmit={async (values) => {
          try {
            if (editingSample) {
              // 对于编辑，使用旧的单文档格式
              const firstDoc = values.documents?.[0];
              if (!firstDoc) {
                message.error('请至少选择一个文档');
                return;
              }
              
              // 构建多文档格式的数据结构匹配后端期望
              const sampleData = {
                question: values.question || '',
                documents: [
                  {
                    documentId: firstDoc.documentId,
                    documentName: firstDoc.documentName,
                    expectedNodeIds: firstDoc.expectedNodeIds
                  }
                ],
                sampleSetId: selectedSampleSet?.id || '',
                sceneType: sceneType,
              };
              
              await updateSample(editingSample.id, sampleData);
              message.success('更新样本成功');
            } else {
              // 对于创建，使用新的多文档格式
              const sampleData = {
                question: values.question || '',
                documents: values.documents?.map(doc => ({
                  document_id: doc.documentId,
                  document_name: doc.documentName,
                  expected_node_ids: doc.expectedNodeIds,
                })) || [],
                sample_set_id: selectedSampleSet?.id || '',
                scene_type: sceneType,
              };
              
              await createSample(sampleData);
              message.success('创建样本成功');
            }

            setSampleModalVisible(false);
            setEditingSample(null);
            if (selectedSampleSet) {
              const response = await getSamples(selectedSampleSet.id);
              setSamples(response.items || []);
            }
          } catch (error) {
            console.error('Error saving sample:', error);
            message.error('保存样本失败: ' + (error instanceof Error ? error.message : '未知错误'));
          }
        }}
      />
    </div>
  );
};

export default SampleManagement; 