import React, { useState, useEffect } from 'react';
import { Table, Spin, Tag, Typography, Button, Modal, Collapse, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { taskApi, getSamples } from '../services/api';

const { Text } = Typography;
const { Panel } = Collapse;

interface LocalSampleResult {
  id: string;
  sample_id: string;
  question: string;
  exec_status: 'success' | 'failed';
  exec_msg?: string;
  recall_accuracy: boolean;
  rerank_accuracy: boolean;
  original_data: any;
  created_at: string;
}

interface Task {
  id: string;
  name: string;
  sample_set_id: string;
  // ... 其他属性
}

interface ResultDetailProps {
  visible: boolean;
  onClose: () => void;
  record: LocalSampleResult;
  task: Task | null;
}

const ResultDetail: React.FC<ResultDetailProps> = ({ visible, onClose, record, task }) => {
  const [sampleInfo, setSampleInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchSampleInfo = async () => {
      if (!visible || !record.sample_id || !task?.sample_set_id) return;

      setLoading(true);
      try {
        const response = await getSamples(task.sample_set_id);
        const sample = response.items?.find((item: any) => item.id === record.sample_id);
        if (sample) {
          setSampleInfo(sample);
        }
      } catch (error) {
        console.error('Error fetching sample info:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSampleInfo();
  }, [visible, record.sample_id, task?.sample_set_id]);

  const renderOriginalData = (originalDataStr: string) => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Spin />
        </div>
      );
    }

    try {
      const data = JSON.parse(originalDataStr);
      const expectedNodeIds = sampleInfo?.documents?.[0]?.expected_node_ids || [];

      return (
        <div style={{ maxHeight: 'calc(100vh - 300px)', overflow: 'hidden' }}>
          {record.exec_msg && (
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">执行信息：</Text>
              <Text>{record.exec_msg}</Text>
            </div>
          )}

          <div style={{ display: 'flex', gap: '24px' }}>
            {/* 左侧预期结果 */}
            <div style={{ flex: 1, overflow: 'auto' }}>
              <div style={{
                padding: '12px 16px',
                background: '#fafafa',
                borderRadius: '8px',
                marginBottom: '16px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '12px'
                }}>
                  <Text strong>预期结果</Text>
                  <Tag color="purple">
                    {sampleInfo?.documents?.[0]?.expected_node_ids?.length || 0} 条结果
                  </Tag>
                </div>
                <div style={{ marginBottom: 12 }}>
                  <Text strong>问题：</Text>
                  <div style={{
                    marginTop: 4,
                    padding: 8,
                    background: '#f5f5f5',
                    borderRadius: 4
                  }}>
                    {sampleInfo?.question || record.question}
                  </div>
                </div>
                <div style={{ maxHeight: 'calc(100vh - 400px)', overflow: 'auto' }}>
                  {sampleInfo?.documents?.[0]?.expected_node_ids?.map((nodeId: string, index: number) => {
                    const nodeContent = sampleInfo?.documents?.[0]?.node_contents?.[nodeId];
                    return (
                      <div key={index} style={{
                        marginBottom: 8,
                        padding: 12,
                        background: '#fff1f0',
                        borderRadius: 4,
                        border: '1px solid #ffa39e'
                      }}>
                        <div><Text strong>节点ID：</Text> {nodeId}</div>
                        <div style={{ marginTop: 4 }}>
                          <Text strong>内容：</Text>
                          {typeof nodeContent === 'string' ? nodeContent : nodeContent?.content || '未找到内容'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* 右侧执行结果 */}
            <div style={{ flex: 1, overflow: 'auto' }}>
              <Collapse defaultActiveKey={['1']} style={{ background: '#fff', border: 'none' }}>
                <Panel
                  header={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Text strong>召回结果</Text>
                      <div>
                        <Tag color="success" style={{ marginRight: 8 }}>
                          匹配 {data.retrieve_results?.filter((node: any) => expectedNodeIds.includes(node.id)).length || 0} 条
                        </Tag>
                        <Tag color="blue">总计 {data.retrieve_results?.length || 0} 条</Tag>
                      </div>
                    </div>
                  }
                  key="1"
                >
                  <div style={{ maxHeight: 'calc(100vh - 450px)', overflow: 'auto' }}>
                    {data.retrieve_results?.map((node: any, index: number) => {
                      const isMatched = expectedNodeIds.includes(node.id);
                      return (
                        <div key={index} style={{
                          marginBottom: 8,
                          padding: 12,
                          background: isMatched ? '#f6ffed' : '#f5f5f5',
                          borderRadius: 4,
                          border: isMatched ? '1px solid #b7eb8f' : 'none'
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text strong>节点ID：{node.id}</Text>
                            {isMatched && <Tag color="success">匹配</Tag>}
                          </div>
                          <div style={{ marginTop: 4 }}><Text strong>内容：</Text> {node.text}</div>
                          <div style={{ marginTop: 4 }}><Text strong>得分：</Text> {node.score.toFixed(4)}</div>
                        </div>
                      );
                    })}
                  </div>
                </Panel>

                {data.rerank_results && (
                  <Panel
                    header={
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Text strong>重排结果</Text>
                        <div>
                          <Tag color="success" style={{ marginRight: 8 }}>
                            匹配 {data.rerank_results.filter((node: any) => expectedNodeIds.includes(node.id)).length || 0} 条
                          </Tag>
                          <Tag color="blue">总计 {data.rerank_results.length} 条</Tag>
                        </div>
                      </div>
                    }
                    key="2"
                  >
                    <div style={{ maxHeight: 'calc(100vh - 450px)', overflow: 'auto' }}>
                      {data.rerank_results.map((node: any, index: number) => {
                        const isMatched = expectedNodeIds.includes(node.id);
                        return (
                          <div key={index} style={{
                            marginBottom: 8,
                            padding: 12,
                            background: isMatched ? '#f6ffed' : '#f5f5f5',
                            borderRadius: 4,
                            border: isMatched ? '1px solid #b7eb8f' : 'none'
                          }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text strong>节点ID：{node.id}</Text>
                              {isMatched && <Tag color="success">匹配</Tag>}
                            </div>
                            <div style={{ marginTop: 4 }}><Text strong>内容：</Text> {node.text}</div>
                            <div style={{ marginTop: 4 }}><Text strong>得分：</Text> {node.score.toFixed(4)}</div>
                          </div>
                        );
                      })}
                    </div>
                  </Panel>
                )}
              </Collapse>
            </div>
          </div>
        </div>
      );
    } catch (error) {
      console.error('Error parsing original data:', error);
      return <Text type="danger">数据解析错误</Text>;
    }
  };

  return (
    <Modal
      title="执行详情"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
    >
      {record.original_data && renderOriginalData(record.original_data)}
    </Modal>
  );
};

const TaskResult: React.FC<{ taskId: string }> = ({ taskId }) => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<LocalSampleResult[]>([]);
  const [selectedRecord, setSelectedRecord] = useState<LocalSampleResult | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [task, setTask] = useState<Task | null>(null);

  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        // 先获取任务信息
        const taskInfo = await taskApi.getTask(taskId);
        setTask(taskInfo);

        // 再获取任务结果
        const data = await taskApi.getTaskResult(taskId);
        if (data && data.items) {
          // 转换API数据格式到本地格式
          const localResults: LocalSampleResult[] = data.items.map((item: any) => ({
            id: item.id?.toString() || '',
            sample_id: item.sample_id || '',
            question: item.question || '',
            exec_status: item.exec_status,
            exec_msg: item.exec_msg,
            recall_accuracy: item.recall_accuracy,
            rerank_accuracy: item.rerank_accuracy,
            original_data: item.original_data,
            created_at: item.created_at,
          }));
          setResults(localResults);
        } else {
          console.error('Task result data is empty or invalid');
          message.error('获取任务结果失败');
        }
      } catch (error) {
        console.error('Error fetching task results:', error);
        message.error('获取任务结果失败');
      } finally {
        setLoading(false);
      }
    };

    if (taskId) {
      fetchResults();
    }
  }, [taskId]);

  const columns: ColumnsType<LocalSampleResult> = [
    {
      title: '样本ID',
      dataIndex: 'sample_id',
      key: 'sample_id',
      width: 200,
    },
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
      width: 300,
      ellipsis: true,
    },
    {
      title: '执行状态',
      dataIndex: 'exec_status',
      key: 'exec_status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'success' ? 'success' : 'error'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      ),
    },
    {
      title: '召回准确性',
      dataIndex: 'recall_accuracy',
      key: 'recall_accuracy',
      width: 100,
      render: (accuracy: boolean) => (
        <Tag color={accuracy ? 'success' : 'error'}>
          {accuracy ? '准确' : '不准确'}
        </Tag>
      ),
    },
    {
      title: '重排准确性',
      dataIndex: 'rerank_accuracy',
      key: 'rerank_accuracy',
      width: 100,
      render: (accuracy: boolean) => (
        <Tag color={accuracy ? 'success' : 'error'}>
          {accuracy ? '准确' : '不准确'}
        </Tag>
      ),
    },
    {
      title: '详细信息',
      key: 'details',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedRecord(record);
            setDetailVisible(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <div>
      {loading ? (
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Spin />
        </div>
      ) : (
        <>
          <Table
            columns={columns}
            dataSource={results}
            rowKey="id"
            scroll={{ y: 'calc(100vh - 300px)' }}
          />
          {selectedRecord && task && (
            <ResultDetail
              visible={detailVisible}
              onClose={() => {
                setDetailVisible(false);
                setSelectedRecord(null);
              }}
              record={selectedRecord}
              task={task}
            />
          )}
        </>
      )}
    </div>
  );
};

export default TaskResult; 