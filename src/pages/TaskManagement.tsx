import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, message, Space, Select, InputNumber } from 'antd';
import type { FormInstance } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import TaskResult from './TaskResult';
import { taskApi, Task, getSampleSets, getCollections } from '../services/api';
import { ColumnsType } from 'antd/es/table';

const TaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [resultVisible, setResultVisible] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [sampleSets, setSampleSets] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedSceneType, setSelectedSceneType] = useState<string>('common');
  const [collections, setCollections] = useState<string[]>([]);
  const [sampleSetMap, setSampleSetMap] = useState<Record<string, string>>({});

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const data = await taskApi.getTasks();
      setTasks(data);
    } catch (error) {
      // 错误已在api层处理
    }
    setLoading(false);
  };

  // 获取样本集列表
  const fetchSampleSets = async (scene_type: string) => {
    try {
      const response = await getSampleSets(scene_type);
      console.info('response:', response);
      
      // 更新样本集映射
      const map: Record<string, string> = {};
      response.items?.forEach((set: any) => {
        map[set.id] = set.name;
      });
      setSampleSetMap(map);
      
      // 更新样本集列表
      setSampleSets(response.items || []);
    } catch (error) {
      message.error('获取样本集失败');
    }
  };

  // 获取 collections 列表
  const fetchCollections = async () => {
    try {
      const data = await getCollections();
      setCollections(data);
    } catch (error) {
      message.error('获取向量库集合列表失败');
    }
  };

  // 创建任务
  const handleCreate = async (values: any) => {
    try {
      await taskApi.createTask(values);
      message.success('创建任务成功');
      setIsModalVisible(false);
      form.resetFields();
      fetchTasks();
    } catch (error) {
      // 错误已在api层处理
    }
  };

  // 删除任务
  const handleDelete = async (id: string) => {
    try {
      await taskApi.deleteTask(id);
      message.success('删除任务成功');
      fetchTasks();
    } catch (error) {
      // 错误已在api层处理
    }
  };

  // 修改查看结果的处理函数
  const handleViewResult = (taskId: string) => {
    setCurrentTaskId(taskId);
    setResultVisible(true);
  };

  const handleStartTask = async (taskId: number) => {
    try {
      await taskApi.executeTask(taskId);
      message.success('任务已开始执行');
      fetchTasks(); // 刷新任务列表
    } catch (error) {
      // 错误已在 api 层处理
    }
  };

  const columns: ColumnsType<Task> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '场景类型',
      dataIndex: 'scene_type',
      key: 'scene_type',
      render: (text: string) => ({
        'common': '通用',
        'bj_telecom': '北京电信',
        'cmcc': '移动'
      }[text] || text),
    },
    {
      title: '样本集',
      dataIndex: 'sample_set_id',
      key: 'sample_set_id',
      render: (id: string) => sampleSetMap[id] || id,
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text: string) => ({
        'recall': '召回测试'
      }[text] || text),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => ({
        'pending': '待执行',
        'running': '执行中',
        'completed': '已完成',
        'failed': '执行失败'
      }[text] || text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '执行结果',
      key: 'result',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>
            成功/失败: {record.success_count}/{record.failed_count}
          </div>
          <div>
            召回准确率: {(record.recall_accuracy_rate * 100).toFixed(2)}%
          </div>
          <div>
            重排准确率: {(record.rerank_accuracy_rate * 100).toFixed(2)}%
          </div>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Task) => (
        <Space>
          {record.status === 'pending' && (
            <Button 
              type="primary"
              onClick={() => handleStartTask(parseInt(record.id))}
            >
              开始执行
            </Button>
          )}
          <Button 
            type="link" 
            onClick={() => {
              setCurrentTaskId(record.id);
              setResultVisible(true);
            }}
          >
            查看结果
          </Button>
          <Button 
            type="link" 
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const TaskForm: React.FC<{
    form: FormInstance;
  }> = ({ form }) => {
    const handleSceneTypeChange = (value: string) => {
      setSelectedSceneType(value);
      form.setFieldsValue({
        scene_type: value,
        sample_set_id: undefined
      });
      fetchSampleSets(value);
    };

    const handleTaskTypeChange = (value: string) => {
      updateDefaultTaskName(form.getFieldValue('scene_type'), value);
    };

    const updateDefaultTaskName = (sceneType: string, taskType: string) => {
      if (!sceneType || !taskType) return;  // 添加空值检查
      
      const sceneTypeMap: Record<string, string> = {
        'common': '通用',
        'bj_telecom': '北京电信',
        'cmcc': '移动'
      };
      const taskTypeMap: Record<string, string> = {
        'recall': '召回测试',
      };

      const sceneName = sceneTypeMap[sceneType] || sceneType;
      const taskTypeName = taskTypeMap[taskType] || taskType;
      
      // 修改时间戳格式，精确到秒
      const now = new Date();
      const timestamp = now.toISOString()
        .replace(/[-:]/g, '')  // 移除日期和时间中的 - 和 :
        .replace(/T/g, '')     // 移除 T
        .replace(/\..+/, '')   // 移除毫秒部分
        .slice(0, 14);         // 保留到秒级 (年月日时分秒)
      
      const defaultName = `${sceneName}_${taskTypeName}_${timestamp}`;
      console.info('生成默认名称:', defaultName);
      
      form.setFieldsValue({
        name: defaultName
      });
    };

    return (
      <Form 
        form={form} 
        onFinish={handleCreate}
        initialValues={{
          scene_type: 'common',
          task_type: 'recall',
          top_k: 50,
          rerank_k: 5
        }}
      >
        <Form.Item
          name="name"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="scene_type"
          label="场景类型"
          rules={[{ required: true, message: '请选择场景类型' }]}
        >
          <Select onChange={handleSceneTypeChange}>
            <Select.Option value="common">通用</Select.Option>
            <Select.Option value="bj_telecom">北京电信</Select.Option>
            <Select.Option value="cmcc">移动</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="task_type"
          label="任务类型"
          rules={[{ required: true, message: '请选择任务类型' }]}
        >
          <Select onChange={handleTaskTypeChange}>
            <Select.Option value="recall">召回测试</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="sample_set_id"
          label="样本集"
          rules={[{ required: true, message: '请选择样本集' }]}
        >
          <Select>
            {sampleSets.map(set => (
              <Select.Option key={set.id} value={set.id}>
                {set.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="collection_name"
          label="向量库集合"
          rules={[{ required: true, message: '请选择向量库集合' }]}
        >
          <Select
            showSearch
            placeholder="请选择向量库集合"
            optionFilterProp="children"
          >
            {collections.map(collection => (
              <Select.Option key={collection} value={collection}>
                {collection}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="top_k"
          label="召回数量"
          rules={[{ required: true, message: '请输入召回数量' }]}
          initialValue={50}
        >
          <InputNumber min={1} />
        </Form.Item>

        <Form.Item
          name="rerank_k"
          label="重排序数量"
          rules={[{ required: true, message: '请输入重排序数量' }]}
          initialValue={5}
        >
          <InputNumber min={1} max={10} />
        </Form.Item>
      </Form>
    );
  };

  const handleModalOpen = () => {
    setIsModalVisible(true);
    // Modal 打开时设置默认名称
    setTimeout(() => {
      form.setFieldsValue({
        scene_type: 'common',
        task_type: 'recall'
      });
      const updateDefaultTaskName = (sceneType: string, taskType: string) => {
        const sceneTypeMap: Record<string, string> = {
          'common': '通用',
          'bj_telecom': '北京电信',
          'cmcc': '移动'
        };
        const taskTypeMap: Record<string, string> = {
          'recall': '召回测试',
          'rerank': '重排序测试'
        };

        const sceneName = sceneTypeMap[sceneType] || sceneType;
        const taskTypeName = taskTypeMap[taskType] || taskType;
        
        // 修改时间戳格式，精确到秒
        const now = new Date();
        const timestamp = now.toISOString()
          .replace(/[-:]/g, '')
          .replace(/T/g, '')
          .replace(/\..+/, '')
          .slice(0, 14);
        
        form.setFieldsValue({
          name: `${sceneName}_${taskTypeName}_${timestamp}`
        });
      };
      updateDefaultTaskName('common', 'recall');
    }, 0);
  };

  useEffect(() => {
    fetchTasks();
    fetchSampleSets('common');
    fetchCollections();
  }, []);

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleModalOpen}
        >
          创建任务
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={tasks}
        loading={loading}
        rowKey="id"
      />

      <Modal
        title="创建召回测试任务"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
      >
        <TaskForm form={form} />
      </Modal>

      {/* 添加结果查看Modal */}
      <Modal
        title="任务结果"
        open={resultVisible}
        onCancel={() => setResultVisible(false)}
        footer={null}
        width={800}
      >
        {currentTaskId && <TaskResult taskId={currentTaskId} />}
      </Modal>
    </div>
  );
};

export default TaskManagement; 