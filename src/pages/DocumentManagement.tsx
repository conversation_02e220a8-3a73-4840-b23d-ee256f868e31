import React, { useState, useEffect } from 'react';
import {
  Table, Select, Typography, Tag, Tooltip, Flex, theme, Segmented, Button, message
} from 'antd';
import {
  FolderOutlined, FileOutlined,
  HomeOutlined, UnorderedListOutlined, ApartmentOutlined, ArrowUpOutlined,
  CheckCircleOutlined, LoadingOutlined, CloseCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import DocumentTree from '../components/DocumentTree';
import type { ColumnsType } from 'antd/es/table';
import { fetchDocuments, getDocumentPreviewUrl, type Document } from '../services/api';
import { SceneType, SceneTypeLabels } from '../types/sample';

const { Title } = Typography;

const DocumentManagement: React.FC = () => {
  const { token } = theme.useToken();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [sceneType, setSceneType] = useState<string>(SceneType.COMMON);
  const [total, setTotal] = useState(0);
  const [currentFolderId, setCurrentFolderId] = useState<string>('-1'); // -1 表示根目录
  const [currentPath, setCurrentPath] = useState<Array<{ id: string, name: string }>>([
    { id: '-1', name: '根目录' }
  ]);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');
  const [allDocuments, setAllDocuments] = useState<Document[]>([]); // 存储所有文档数据
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [allTotal, setAllTotal] = useState(0); // 所有文档的总数（用于树形视图）

  const fetchData = async (page: number = currentPage, size: number = pageSize) => {
    setLoading(true);
    try {
      // 获取所有文档数据（用于树形视图）
      const allDocsResponse = await fetchDocuments({
        scene_type: sceneType,
        page: 1,
        page_size: 1000, // 获取所有数据
        order_by: 'id',
        order: 'desc',
      });
      
      const allDocs = allDocsResponse.items || [];
      setAllDocuments(allDocs);
      setAllTotal(allDocsResponse.total || 0);
      
      // 获取当前文件夹的文档数据（用于表格视图）
      const currentFolderResponse = await fetchDocuments({
        scene_type: sceneType,
        parent_id: currentFolderId === '-1' ? -1 : parseInt(currentFolderId),
        page: page,
        page_size: size,
        order_by: 'id',
        order: 'desc',
      });
      
      let currentFolderDocs = currentFolderResponse.items || [];
      let currentTotal = currentFolderResponse.total || 0;
      
      // 如果在根目录（首页），只显示文件夹，不显示文件
      if (currentFolderId === '-1') {
        // 注意：这里需要在服务端过滤，但目前我们在客户端过滤
        // 为了正确的分页，我们需要获取更多数据然后过滤
        const allCurrentFolderResponse = await fetchDocuments({
          scene_type: sceneType,
          parent_id: -1,
          page: 1,
          page_size: 1000, // 获取所有根目录数据
          order_by: 'id',
          order: 'desc',
        });
        
        const allCurrentFolderDocs = allCurrentFolderResponse.items || [];
        const filteredDocs = allCurrentFolderDocs.filter(doc => doc.is_dir);
        
        // 手动分页
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        currentFolderDocs = filteredDocs.slice(startIndex, endIndex);
        currentTotal = filteredDocs.length;
      }
      
      setDocuments(currentFolderDocs);
      setTotal(currentTotal);
    } catch (error) {
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    setCurrentPage(1); // 切换场景或文件夹时重置到第一页
    fetchData(1, pageSize);
  }, [sceneType, currentFolderId]);

  useEffect(() => {
    fetchData(currentPage, pageSize);
  }, [currentPage, pageSize]);





  const columns: ColumnsType<Document> = [
    {
      title: '文档ID',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
      render: (text: string) => <Typography.Text code>{text}</Typography.Text>
    },
    {
      title: '文档名称',
      dataIndex: 'file_name',
      key: 'file_name',
      width: '75%',
      render: (text: string, record: Document) => {
        const getFileIcon = (fileName: string, isDir: boolean | undefined) => {
          if (isDir) {
            return <FolderOutlined style={{ color: token.colorWarning, fontSize: 18 }} />;
          }
          
          const ext = fileName?.split('.').pop()?.toLowerCase();
          const iconColor = {
            'pdf': '#ff4d4f',
            'doc': '#1890ff',
            'docx': '#1890ff',
            'txt': '#8c8c8c',
            'md': '#722ed1',
            'xlsx': '#52c41a',
            'xls': '#52c41a',
            'ppt': '#fa8c16',
            'pptx': '#fa8c16',
          }[ext || ''] || token.colorTextSecondary;
          
          return <FileOutlined style={{ color: iconColor, fontSize: 16 }} />;
        };

        return (
          <Flex align="center" gap="small" style={{ cursor: 'pointer' }}>
            {getFileIcon(text, record.is_dir)}
            <Typography.Text 
              strong={record.is_dir}
              style={{ 
                color: record.is_dir ? token.colorText : token.colorTextSecondary,
                cursor: 'pointer'
              }}
              onClick={() => {
                if (record.is_dir) {
                  handleOpenFolder(record);
                } else {
                  handlePreview(record);
                }
              }}
            >
              {text}
            </Typography.Text>
          </Flex>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'upload_status',
      key: 'upload_status',
      width: '15%',
      render: (text: string, record: Document) => {
        if (record.is_dir || !text) {
          return <Typography.Text type="secondary">-</Typography.Text>;
        }

        const statusConfig: Record<string, { text: string; color: string; icon: React.ReactNode }> = {
          'UPLOAD_COMPLETED': { 
            text: '上传完成', 
            color: 'success', 
            icon: <CheckCircleOutlined /> 
          },
          'INDEX_BUILDING': { 
            text: '索引构建中', 
            color: 'processing', 
            icon: <LoadingOutlined spin /> 
          },
          'INDEX_BUILD_SUCCESS': { 
            text: '索引构建成功', 
            color: 'success', 
            icon: <CheckCircleOutlined /> 
          },
          'INDEX_BUILD_FAILED': { 
            text: '索引构建失败', 
            color: 'error', 
            icon: <CloseCircleOutlined /> 
          },
        };

        const config = statusConfig[text] || { 
          text: text || '未知', 
          color: 'default' as const, 
          icon: <ExclamationCircleOutlined /> 
        };
        
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
  ];

  const handlePreview = async (document: Document) => {
    if (document.is_dir) {
      message.info('文件夹无法预览');
      return;
    }

    try {
      const url = await getDocumentPreviewUrl(document.id, document.file_name || '', document.scene_type || sceneType);
      // 在新窗口中打开文档预览
      window.open(url, '_blank');
    } catch (error) {
      message.error('获取文档预览链接失败');
      console.error('Preview error:', error);
    }
  };





  const handleOpenFolder = (folder: Document) => {
    // 进入文件夹
    setCurrentFolderId(folder.id);
    
    // 更新面包屑路径
    const newPath = [...currentPath, { id: folder.id, name: folder.file_name || '未命名文件夹' }];
    setCurrentPath(newPath);
    
    message.success(`已进入文件夹: ${folder.file_name}`);
  };

  // 面包屑导航点击处理
  const handleBreadcrumbClick = (pathItem: { id: string, name: string }, index: number) => {
    if (index === currentPath.length - 1) {
      // 点击的是当前路径，不需要操作
      return;
    }
    
    // 导航到指定路径
    setCurrentFolderId(pathItem.id);
    
    // 更新面包屑路径，移除点击项之后的所有路径
    const newPath = currentPath.slice(0, index + 1);
    setCurrentPath(newPath);
    
    message.success(`已导航到: ${pathItem.name}`);
  };

  // 分页处理函数
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  const handlePageSizeChange = (current: number, size: number) => {
    setCurrentPage(1); // 改变页面大小时重置到第一页
    setPageSize(size);
  };

  return (
    <div style={{ 
      padding: '32px',
      background: token.colorBgLayout,
      minHeight: '100vh'
    }}>
      {/* 现代化页面头部 */}
      <div style={{ 
        marginBottom: 32,
        paddingBottom: 24,
        borderBottom: `1px solid ${token.colorBorderSecondary}`
      }}>
        <Flex justify="space-between" align="center">
          <div>
            <Title level={2} style={{ 
              margin: 0, 
              color: token.colorText,
              fontSize: 28,
              fontWeight: 700,
              letterSpacing: '-0.025em'
            }}>
              📁 文档管理
            </Title>
            <Typography.Text style={{ 
              color: token.colorTextSecondary,
              fontSize: 16,
              marginTop: 4,
              display: 'block'
            }}>
              浏览和查看您的文档资源，支持层级结构和多种视图模式
            </Typography.Text>
          </div>
          
          {/* 快速统计 */}
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: 12,
            padding: '16px 24px',
            color: 'white',
            textAlign: 'center',
            minWidth: 120
          }}>
            <div style={{ fontSize: 24, fontWeight: 700, lineHeight: 1 }}>
              {total}
            </div>
            <div style={{ fontSize: 12, opacity: 0.9, marginTop: 4 }}>
              {currentFolderId === '-1' ? '个文件夹' : '个项目'}
            </div>
          </div>
        </Flex>
      </div>

      {/* 现代化工具栏 */}
      <div style={{
        background: token.colorBgContainer,
        border: `1px solid ${token.colorBorder}`,
        borderRadius: 12,
        padding: '20px 24px',
        marginBottom: 24,
        boxShadow: token.boxShadow
      }}>
        <Flex justify="space-between" align="center" wrap="wrap" gap="large">
          {/* 左侧控制区 */}
          <Flex align="center" gap="large" wrap="wrap">
            {/* 场景选择器 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <Typography.Text strong style={{ 
                color: token.colorText,
                fontSize: 14,
                whiteSpace: 'nowrap'
              }}>
                场景类型
              </Typography.Text>
              <Select
                value={sceneType}
                onChange={setSceneType}
                style={{ width: 180 }}
                size="middle"
                options={Object.entries(SceneTypeLabels).map(([value, label]) => ({
                  value,
                  label,
                }))}
              />
            </div>

            {/* 现代化面包屑 */}
            <div style={{
              background: token.colorFillSecondary,
              borderRadius: 8,
              padding: '8px 16px',
              display: 'flex',
              alignItems: 'center',
              gap: 8
            }}>
              {currentPath.map((path, index) => (
                <React.Fragment key={path.id}>
                  {index > 0 && (
                    <span style={{ color: token.colorTextTertiary, fontSize: 12 }}>
                      /
                    </span>
                  )}
                  <span
                    onClick={() => handleBreadcrumbClick(path, index)}
                    style={{
                      color: index === currentPath.length - 1 ? token.colorText : token.colorTextSecondary,
                      cursor: index < currentPath.length - 1 ? 'pointer' : 'default',
                      fontSize: 13,
                      fontWeight: index === currentPath.length - 1 ? 600 : 400,
                      padding: '4px 8px',
                      borderRadius: 4,
                      transition: 'all 0.15s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 4
                    }}
                    onMouseEnter={(e) => {
                      if (index < currentPath.length - 1) {
                        e.currentTarget.style.background = token.colorFillTertiary;
                        e.currentTarget.style.color = token.colorPrimary;
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (index < currentPath.length - 1) {
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.style.color = token.colorTextSecondary;
                      }
                    }}
                  >
                    {index === 0 ? <HomeOutlined style={{ fontSize: 12 }} /> : <FolderOutlined style={{ fontSize: 12 }} />}
                    {path.name}
                  </span>
                </React.Fragment>
              ))}
            </div>
          </Flex>

          {/* 右侧操作区 */}
          <Flex align="center" gap="small" wrap="wrap">
            {/* 返回按钮 */}
            {currentFolderId !== '-1' && (
              <Button 
                icon={<ArrowUpOutlined />}
                onClick={() => {
                  if (currentPath.length > 1) {
                    const parentPath = currentPath[currentPath.length - 2];
                    handleBreadcrumbClick(parentPath, currentPath.length - 2);
                  }
                }}
                style={{ borderRadius: 8 }}
              >
                返回上级
              </Button>
            )}
            


            {/* 视图切换器 */}
            <Segmented
              value={viewMode}
              onChange={setViewMode}
              options={[
                { 
                  label: '列表视图', 
                  value: 'table', 
                  icon: <UnorderedListOutlined /> 
                },
                { 
                  label: '树形视图', 
                  value: 'tree', 
                  icon: <ApartmentOutlined /> 
                }
              ]}
              style={{
                background: token.colorFillSecondary,
                borderRadius: 8,
                padding: 2
              }}
            />
          </Flex>
        </Flex>
      </div>

      {/* 现代化内容区域 */}
      <div style={{
        background: token.colorBgContainer,
        border: `1px solid ${token.colorBorder}`,
        borderRadius: 12,
        overflow: 'hidden',
        boxShadow: token.boxShadow
      }}>
        {viewMode === 'table' ? (
          <Table
            columns={columns}
            dataSource={documents}
            loading={loading}
            rowKey="id"
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: handlePageChange,
              onShowSizeChange: handlePageSizeChange,
              style: {
                padding: '16px 24px',
                borderTop: `1px solid ${token.colorBorderSecondary}`,
                background: token.colorFillAlter
              }
            }}
            scroll={{ x: 800 }}
            size="middle"
            showHeader={true}
            bordered={false}
            rowClassName={(record) => record.is_dir ? 'folder-row' : ''}
            locale={{
              emptyText: (
                <div style={{ padding: '60px 20px', textAlign: 'center' }}>
                  <div style={{
                    fontSize: 48,
                    marginBottom: 16,
                    opacity: 0.3
                  }}>
                    📁
                  </div>
                  <Typography.Title level={4} style={{ 
                    color: token.colorTextSecondary,
                    marginBottom: 8
                  }}>
                    {currentFolderId === '-1' ? '暂无文件夹' : '当前文件夹为空'}
                  </Typography.Title>
                  <Typography.Text style={{ color: token.colorTextTertiary }}>
                    {currentFolderId === '-1' 
                      ? '暂无文件夹可浏览' 
                      : '当前文件夹暂无内容'
                    }
                  </Typography.Text>
                </div>
              )
            }}
            style={{
              background: 'transparent'
            }}
          />
        ) : (
          <div style={{ 
            padding: 24,
            background: token.colorBgContainer
          }}>
            <DocumentTree
              documents={allDocuments}
              onPreview={handlePreview}
            />
          </div>
        )}
      </div>


    </div>
  );
};

export default DocumentManagement; 