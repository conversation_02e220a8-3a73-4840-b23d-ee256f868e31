import React from 'react';
import { Card, Row, Col, Button, Typography, Space, Statistic, theme, Flex } from 'antd';
import { Link } from 'react-router-dom';
import {
  FolderOutlined,
  FileTextOutlined,
  SearchOutlined,
  BarChartOutlined,
  SettingOutlined,
  RocketOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  const { token } = theme.useToken();
  const quickActions = [
    {
      title: '文档管理',
      description: '上传、组织和管理您的文档资源',
      icon: <FolderOutlined style={{ fontSize: 28, color: '#0969da' }} />,
      link: '/document-management',
      gradient: 'linear-gradient(135deg, #ddf4ff 0%, #b6e3ff 100%)',
      borderColor: '#0969da'
    },
    {
      title: '文档解析预览',
      description: '预览文档解析结果和节点信息',
      icon: <FileTextOutlined style={{ fontSize: 28, color: '#1a7f37' }} />,
      link: '/document-preview',
      gradient: 'linear-gradient(135deg, #dafbe1 0%, #aceebb 100%)',
      borderColor: '#1a7f37'
    },
    {
      title: '节点预览',
      description: '查看和搜索文档节点内容',
      icon: <SearchOutlined style={{ fontSize: 28, color: '#d1242f' }} />,
      link: '/node-preview',
      gradient: 'linear-gradient(135deg, #fff8c5 0%, #fae17d 100%)',
      borderColor: '#d1242f'
    },
    {
      title: '样本管理',
      description: '创建和管理测试样本集',
      icon: <BarChartOutlined style={{ fontSize: 28, color: '#8250df' }} />,
      link: '/samples',
      gradient: 'linear-gradient(135deg, #fbefff 0%, #e9d5ff 100%)',
      borderColor: '#8250df'
    },
    {
      title: '任务管理',
      description: '执行和监控评估任务',
      icon: <SettingOutlined style={{ fontSize: 28, color: '#cf222e' }} />,
      link: '/tasks',
      gradient: 'linear-gradient(135deg, #ffebe9 0%, #ffc1cc 100%)',
      borderColor: '#cf222e'
    }
  ];

  return (
    <div style={{ 
      padding: '24px', 
      background: token.colorBgLayout, 
      minHeight: '100vh' 
    }}>
      {/* 欢迎区域 */}
      <Card 
        style={{ 
          marginBottom: 24,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          borderRadius: token.borderRadiusLG,
          boxShadow: token.boxShadowSecondary
        }}
        styles={{ body: { padding: '48px 40px' } }}
      >
        <Flex justify="space-between" align="center">
          <Space direction="vertical" size="large">
            <Title level={1} style={{ color: 'white', margin: 0 }}>
              <RocketOutlined style={{ marginRight: 16 }} />
              文档评估工作室
            </Title>
            <Paragraph style={{ 
              color: 'rgba(255,255,255,0.9)', 
              fontSize: 18, 
              margin: 0,
              maxWidth: 600
            }}>
              智能文档处理与评估平台，帮助您高效管理文档、创建样本集并执行评估任务
            </Paragraph>
          </Space>
          
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>系统状态</span>}
            value="运行中"
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a', fontSize: 24 }}
          />
        </Flex>
      </Card>

      {/* 快速操作区域 */}
      <Title level={2} style={{ marginBottom: 24, color: token.colorText }}>
        🚀 快速开始
      </Title>
      
      <Row gutter={[24, 24]}>
        {quickActions.map((action, index) => (
          <Col xs={24} sm={12} lg={8} xl={6} key={index}>
            <Card
              hoverable
              style={{
                height: 240,
                background: action.gradient,
                border: `1px solid ${action.borderColor}20`,
                borderRadius: 16,
                boxShadow: '0 2px 8px rgba(16, 24, 40, 0.08)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                overflow: 'hidden'
              }}
              styles={{
                body: {
                  padding: 24,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  position: 'relative',
                  zIndex: 2
                }
              }}
            >
              {/* 装饰性背景元素 */}
              <div style={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                background: `${action.borderColor}10`,
                borderRadius: '50%',
                zIndex: 1
              }} />
              
              <Space direction="vertical" size="middle" style={{ width: '100%', zIndex: 2 }}>
                <div style={{ 
                  padding: 12,
                  background: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: 12,
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 56,
                  height: 56,
                  boxShadow: '0 2px 8px rgba(16, 24, 40, 0.1)'
                }}>
                  {action.icon}
                </div>
                <Title level={4} style={{ 
                  margin: 0, 
                  color: token.colorText,
                  fontSize: 18,
                  fontWeight: 600,
                  letterSpacing: '-0.01em'
                }}>
                  {action.title}
                </Title>
                <Paragraph style={{ 
                  color: token.colorTextSecondary, 
                  margin: 0, 
                  fontSize: 14,
                  lineHeight: 1.6
                }}>
                  {action.description}
                </Paragraph>
              </Space>
              
              <Link to={action.link}>
                <Button 
                  type="primary" 
                  block 
                  size="large"
                  style={{ 
                    marginTop: 20,
                    borderRadius: 8,
                    fontWeight: 600,
                    height: 44,
                    fontSize: 14,
                    background: action.borderColor,
                    borderColor: action.borderColor,
                    boxShadow: `0 2px 8px ${action.borderColor}30`
                  }}
                >
                  立即使用 →
                </Button>
              </Link>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 功能特性区域 */}
      <Title level={2} style={{ 
        marginTop: 48, 
        marginBottom: 24, 
        color: token.colorText 
      }}>
        ✨ 核心功能
      </Title>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} md={8}>
          <Card
            title="📁 智能文档管理"
            style={{ 
              height: '100%',
              borderRadius: token.borderRadiusLG,
              boxShadow: token.boxShadowTertiary
            }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Typography.Text>• 支持多种文档格式上传</Typography.Text>
              <Typography.Text>• 目录层级结构管理</Typography.Text>
              <Typography.Text>• 文档预览和下载</Typography.Text>
              <Typography.Text>• 批量操作和搜索</Typography.Text>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} md={8}>
          <Card
            title="🔍 文档解析预览"
            style={{ 
              height: '100%',
              borderRadius: token.borderRadiusLG,
              boxShadow: token.boxShadowTertiary
            }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Typography.Text>• 实时文档解析预览</Typography.Text>
              <Typography.Text>• 节点内容可视化</Typography.Text>
              <Typography.Text>• 元数据信息展示</Typography.Text>
              <Typography.Text>• 解析参数配置</Typography.Text>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} md={8}>
          <Card
            title="📊 样本与任务管理"
            style={{ 
              height: '100%',
              borderRadius: token.borderRadiusLG,
              boxShadow: token.boxShadowTertiary
            }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Typography.Text>• 样本集创建和管理</Typography.Text>
              <Typography.Text>• 评估任务配置</Typography.Text>
              <Typography.Text>• 任务执行监控</Typography.Text>
              <Typography.Text>• 结果统计分析</Typography.Text>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 底部信息 */}
      <Card 
        style={{ 
          marginTop: 48,
          textAlign: 'center',
          background: token.colorFillAlter,
          borderRadius: token.borderRadiusLG,
          boxShadow: token.boxShadowTertiary
        }}
      >
        <Typography.Text type="secondary">
          文档评估工作室 v1.0.0 | 基于 React + FastAPI 构建
        </Typography.Text>
      </Card>
    </div>
  );
};

export default Home;