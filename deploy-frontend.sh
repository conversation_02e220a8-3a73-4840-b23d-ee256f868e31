#!/bin/bash

npm run build

path="/data/home/<USER>/doc-evaluate-studio"
echo "开始部署前端"
echo $path
scp -r build/* yuanyuan@192.168.110.100:$path/dist/
scp nginx.conf yuanyuan@192.168.110.100:$path/
scp compose.yaml yuanyuan@192.168.110.100:$path/
scp dockerfile yuanyuan@192.168.110.100:$path/

ssh yuanyuan@192.168.110.100 "cd $path && DOCKER_BUILDKIT=0 docker compose -f compose.yaml build && docker compose -f compose.yaml up -d"

