{"name": "doc-evaluate-studio", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.5.2", "antd": "^5.22.7", "cra-template-typescript": "1.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.1.1", "react-scripts": "5.0.1", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:prod": "env-cmd -f .env.production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "env-cmd": "^10.1.0"}}