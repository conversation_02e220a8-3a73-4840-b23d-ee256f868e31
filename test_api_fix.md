# API修复验证

## 修复的问题

### 1. 422 Unprocessable Entity 错误
**原因**: 
- `keyword` 参数类型不匹配：后端期望 `Optional[str]`，但定义为 `str`
- `page_size` 参数超出限制：前端传递 1000，但后端限制最大为 100

**修复**:
```python
# 后端修复
keyword: Optional[str] = Query(None, description="搜索关键词"),
page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
```

```typescript
// 前端优化
// 只有当keyword有值时才添加到查询参数中
if (params.keyword) {
  searchParams.append('keyword', params.keyword);
}
```

## 修复后的API调用流程

### 1. 获取所有文档（树形视图）
```typescript
const allDocsResponse = await fetchDocuments({
  scene_type: sceneType,
  page: 1,
  page_size: 1000, // 现在支持最大1000
});
```
**生成的URL**: `/api/documents/?scene_type=common&page=1&page_size=1000`

### 2. 获取当前文件夹文档（表格视图）
```typescript
const currentFolderResponse = await fetchDocuments({
  scene_type: sceneType,
  parent_id: currentFolderId === '-1' ? -1 : parseInt(currentFolderId),
  page: 1,
  page_size: 1000,
});
```
**生成的URL**: `/api/documents/?scene_type=common&page=1&page_size=1000&parent_id=-1`

### 3. 带关键词搜索
```typescript
const searchResponse = await fetchDocuments({
  scene_type: sceneType,
  keyword: 'test',
  parent_id: -1,
  page: 1,
  page_size: 100,
});
```
**生成的URL**: `/api/documents/?scene_type=common&page=1&page_size=100&keyword=test&parent_id=-1`

## 预期结果

现在API调用应该能够正常工作，不再出现422错误：
- ✅ 支持获取所有文档（不传parent_id）
- ✅ 支持获取根目录文档（parent_id=-1）
- ✅ 支持获取子文件夹文档（parent_id=具体ID）
- ✅ 支持关键词搜索（可选）
- ✅ 支持大页面大小（最大1000）

## 测试用例

1. **根目录文档**: `GET /api/documents/?scene_type=common&page=1&page_size=100&parent_id=-1`
2. **所有文档**: `GET /api/documents/?scene_type=common&page=1&page_size=1000`
3. **子文件夹**: `GET /api/documents/?scene_type=common&page=1&page_size=100&parent_id=123`
4. **搜索**: `GET /api/documents/?scene_type=common&page=1&page_size=100&keyword=test&parent_id=-1`