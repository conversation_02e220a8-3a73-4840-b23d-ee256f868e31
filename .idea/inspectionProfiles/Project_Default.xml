<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="15">
            <item index="0" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="1" class="java.lang.String" itemvalue="openai" />
            <item index="2" class="java.lang.String" itemvalue="protobuf" />
            <item index="3" class="java.lang.String" itemvalue="fastapi" />
            <item index="4" class="java.lang.String" itemvalue="langchain" />
            <item index="5" class="java.lang.String" itemvalue="transformers" />
            <item index="6" class="java.lang.String" itemvalue="pandas" />
            <item index="7" class="java.lang.String" itemvalue="tqdm" />
            <item index="8" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="9" class="java.lang.String" itemvalue="grpcio" />
            <item index="10" class="java.lang.String" itemvalue="requests" />
            <item index="11" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="12" class="java.lang.String" itemvalue="Jinja2" />
            <item index="13" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="14" class="java.lang.String" itemvalue="PyMuPDF" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
          <option value="N802" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="askbot_common.generated.common.header_pb2.ProjectTypeName.*" />
          <option value="askbot_common.generated.common.header_pb2.AgrumentKey.*" />
          <option value="askbot_common.generated.common.header_pb2.ModuleName.*" />
          <option value="dict.STATISTIC_TOP" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="Stylelint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="TsLint" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>